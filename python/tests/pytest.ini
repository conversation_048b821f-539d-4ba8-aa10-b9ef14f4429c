[tool:pytest]
minversion = 7.0
addopts =
    -ra
    -q
    --strict-markers
    --strict-config
    --cov=atom
    --cov-report=html:coverage/python/html
    --cov-report=xml:coverage/python/coverage.xml
    --cov-report=term-missing
    --cov-branch
    --cov-fail-under=80
testpaths =
    python/tests
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    benchmark: marks tests as performance benchmarks
    unit: marks tests as unit tests
    functional: marks tests as functional tests
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S
