# {{title}}

{{#description}}
> {{description}}
{{/description}}

{{#timestamp}}
**Generated at:** {{timestamp}}
{{/timestamp}}

{{#author}}
**Author:** {{author}}
{{/author}}

---

{{content}}

---

## Summary

This report was generated using the Atom System Information Printer, a comprehensive system information gathering and reporting tool.

### Features

- 🖥️ **Comprehensive System Analysis** - Detailed information about all system components
- 📊 **Multiple Export Formats** - HTML, JSON, Markdown, XML, and CSV support
- 🎨 **Customizable Formatting** - Multiple styling options and themes
- 📈 **Performance Metrics** - Real-time system performance data
- 🔒 **Security Information** - Security-related system details
- 📱 **Responsive Design** - Works on desktop and mobile devices

### System Components Analyzed

- **Operating System** - Version, kernel, architecture details
- **CPU** - Model, cores, frequency, temperature, usage
- **Memory** - Total, available, usage statistics
- **Storage** - Disk information, capacity, usage
- **Battery** - Level, health, charging status (if applicable)
- **BIOS/UEFI** - Firmware information and settings
- **Network** - Connection details and statistics
- **Graphics** - GPU information and capabilities

### Usage

This report can be used for:

- System inventory and documentation
- Performance monitoring and analysis
- Troubleshooting system issues
- Hardware upgrade planning
- Security auditing
- Compliance reporting

---

*Generated by Atom System Information Printer*
