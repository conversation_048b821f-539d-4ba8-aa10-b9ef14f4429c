#include "base_exporter.hpp"
#include <spdlog/spdlog.h>
#include <format>
#include <chrono>
#include <iomanip>
#include <sstream>
#include <filesystem>
#include <fstream>

namespace atom::system {

void BaseExporter::setOptions(const ExportOptions& options) {
    options_ = options;
}

auto BaseExporter::getOptions() const -> const ExportOptions& {
    return options_;
}

void BaseExporter::setCustomOption(const std::string& key, const std::string& value) {
    options_.customOptions[key] = value;
}

auto BaseExporter::getCustomOption(const std::string& key, const std::string& defaultValue) const -> std::string {
    auto it = options_.customOptions.find(key);
    return (it != options_.customOptions.end()) ? it->second : defaultValue;
}

auto BaseExporter::addMetadata(const std::string& content) const -> std::string {
    if (!options_.includeMetadata) {
        return content;
    }

    // This is a base implementation - derived classes should override for format-specific metadata
    std::stringstream ss;
    ss << "<!-- Generated by Atom System Information Printer -->\n";
    ss << "<!-- Title: " << options_.title << " -->\n";
    if (!options_.author.empty()) {
        ss << "<!-- Author: " << options_.author << " -->\n";
    }
    if (!options_.description.empty()) {
        ss << "<!-- Description: " << options_.description << " -->\n";
    }
    ss << content;

    return ss.str();
}

auto BaseExporter::addTimestamp(const std::string& content) const -> std::string {
    if (!options_.includeTimestamp) {
        return content;
    }

    auto timestamp = getCurrentTimestamp();
    return std::format("Generated at: {}\n\n{}", timestamp, content);
}

auto BaseExporter::escapeText(const std::string& text) const -> std::string {
    // Base implementation - no escaping
    // Derived classes should override for format-specific escaping
    return text;
}

auto BaseExporter::loadTemplate(const std::string& templatePath) const -> std::string {
    try {
        std::ifstream file(templatePath);
        if (!file.is_open()) {
            spdlog::error("Failed to open template file: {}", templatePath);
            return "";
        }

        std::stringstream buffer;
        buffer << file.rdbuf();
        return buffer.str();
    } catch (const std::exception& e) {
        spdlog::error("Error loading template {}: {}", templatePath, e.what());
        return "";
    }
}

auto BaseExporter::replacePlaceholders(const std::string& templateContent,
                                      const std::unordered_map<std::string, std::string>& placeholders) const -> std::string {
    std::string result = templateContent;

    for (const auto& [placeholder, value] : placeholders) {
        std::string target = "{{" + placeholder + "}}";
        size_t pos = 0;
        while ((pos = result.find(target, pos)) != std::string::npos) {
            result.replace(pos, target.length(), value);
            pos += value.length();
        }
    }

    return result;
}

bool BaseExporter::writeToFile(const std::string& content, const std::string& filename) const {
    try {
        if (!validateAndPrepareFile(filename)) {
            return false;
        }

        std::ofstream file(filename);
        if (!file.is_open()) {
            spdlog::error("Failed to open file for writing: {}", filename);
            return false;
        }

        file << content;
        file.close();

        if (file.fail()) {
            spdlog::error("Error writing to file: {}", filename);
            return false;
        }

        // Log file size
        auto fileSize = std::filesystem::file_size(filename);
        spdlog::info("Successfully exported to {} ({})", filename, formatFileSize(fileSize));

        return true;
    } catch (const std::exception& e) {
        spdlog::error("Exception while writing to file {}: {}", filename, e.what());
        return false;
    }
}

bool BaseExporter::validateAndPrepareFile(const std::string& filename) const {
    if (filename.empty()) {
        spdlog::error("Empty filename provided");
        return false;
    }

    try {
        std::filesystem::path filePath(filename);

        // Create parent directories if they don't exist
        if (filePath.has_parent_path()) {
            std::filesystem::create_directories(filePath.parent_path());
        }

        return true;
    } catch (const std::exception& e) {
        spdlog::error("Error preparing file path {}: {}", filename, e.what());
        return false;
    }
}

auto BaseExporter::getCurrentTimestamp() const -> std::string {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);

    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    return ss.str();
}

auto BaseExporter::formatFileSize(size_t bytes) const -> std::string {
    const char* units[] = {"B", "KB", "MB", "GB", "TB"};
    const size_t numUnits = sizeof(units) / sizeof(units[0]);

    double size = static_cast<double>(bytes);
    size_t unitIndex = 0;

    while (size >= 1024.0 && unitIndex < numUnits - 1) {
        size /= 1024.0;
        ++unitIndex;
    }

    return std::format("{:.2f} {}", size, units[unitIndex]);
}

bool BaseExporter::createDirectories(const std::string& filepath) const {
    try {
        std::filesystem::path path(filepath);
        if (path.has_parent_path()) {
            return std::filesystem::create_directories(path.parent_path());
        }
        return true;
    } catch (const std::exception& e) {
        spdlog::error("Error creating directories for {}: {}", filepath, e.what());
        return false;
    }
}

} // namespace atom::system
