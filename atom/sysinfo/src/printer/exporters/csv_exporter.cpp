#include "csv_exporter.hpp"
#include <spdlog/spdlog.h>
#include <sstream>
#include <regex>

namespace atom::system {

CsvExporter::CsvExporter(const ExportOptions& options) {
    setOptions(options);
}

bool CsvExporter::exportToFile(const std::string& content, const std::string& filename) {
    auto csvContent = exportToString(content);
    return writeToFile(csvContent, filename);
}

auto CsvExporter::exportToString(const std::string& content) -> std::string {
    std::stringstream ss;

    // Add metadata as comments if enabled
    if (options_.includeMetadata) {
        ss << "# " << options_.title << "\n";
        ss << "# Generated by Atom System Information Printer\n";
        if (!options_.author.empty()) {
            ss << "# Author: " << options_.author << "\n";
        }
        if (options_.includeTimestamp) {
            ss << "# Generated at: " << getCurrentTimestamp() << "\n";
        }
        ss << "#\n";
    }

    // CSV header
    ss << "Component,Property,Value\n";

    // Convert content to CSV
    ss << convertToCsv(content);

    return ss.str();
}

auto CsvExporter::getFileExtension() const -> std::string {
    return ".csv";
}

auto CsvExporter::getMimeType() const -> std::string {
    return "text/csv";
}

auto CsvExporter::convertToCsv(const std::string& content) const -> std::string {
    std::stringstream ss;
    std::istringstream iss(content);
    std::string line;
    std::string currentComponent;

    while (std::getline(iss, line)) {
        // Skip empty lines and borders
        if (line.empty() || line.find("===") != std::string::npos ||
            line.find("---") != std::string::npos) {
            continue;
        }

        // Check if this is a header line (component name)
        if (line.find('|') == std::string::npos && !line.empty()) {
            currentComponent = escapeCsvField(line);
            continue;
        }

        // Parse table rows
        if (line.find('|') != std::string::npos) {
            // Extract property and value from table row
            std::regex rowRegex("\\|\\s*([^|]+)\\s*\\|\\s*([^|]+)\\s*\\|");
            std::smatch matches;

            if (std::regex_search(line, matches, rowRegex)) {
                std::string property = matches[1].str();
                std::string value = matches[2].str();

                // Trim whitespace
                property = std::regex_replace(property, std::regex("^\\s+|\\s+$"), "");
                value = std::regex_replace(value, std::regex("^\\s+|\\s+$"), "");

                if (!property.empty() && !value.empty()) {
                    ss << escapeCsvField(currentComponent) << ","
                       << escapeCsvField(property) << ","
                       << escapeCsvField(value) << "\n";
                }
            }
        }
    }

    return ss.str();
}

auto CsvExporter::escapeText(const std::string& text) const -> std::string {
    return escapeCsvField(text);
}

auto CsvExporter::escapeCsvField(const std::string& field) const -> std::string {
    std::string result = field;

    // If field contains comma, quote, or newline, wrap in quotes and escape quotes
    if (result.find(',') != std::string::npos ||
        result.find('"') != std::string::npos ||
        result.find('\n') != std::string::npos) {

        // Escape quotes by doubling them
        std::regex quoteRegex("\"");
        result = std::regex_replace(result, quoteRegex, "\"\"");

        // Wrap in quotes
        result = "\"" + result + "\"";
    }

    return result;
}

} // namespace atom::system
