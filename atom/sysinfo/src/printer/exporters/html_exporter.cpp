#include "html_exporter.hpp"
#include <spdlog/spdlog.h>
#include <format>
#include <sstream>
#include <regex>

namespace atom::system {

HtmlExporter::HtmlExporter(const ExportOptions& options) {
    setOptions(options);
}

bool HtmlExporter::exportToFile(const std::string& content, const std::string& filename) {
    auto htmlContent = exportToString(content);
    return writeToFile(htmlContent, filename);
}

auto HtmlExporter::exportToString(const std::string& content) -> std::string {
    auto htmlContent = convertToHtml(content);
    auto document = generateHtmlDocument(htmlContent);
    return addMetadata(document);
}

auto HtmlExporter::getFileExtension() const -> std::string {
    return ".html";
}

auto HtmlExporter::getMimeType() const -> std::string {
    return "text/html";
}

auto HtmlExporter::convertToHtml(const std::string& content) const -> std::string {
    std::string htmlContent = escapeText(content);

    // Convert ASCII tables to HTML tables
    htmlContent = convertTableToHtml(htmlContent);

    // Convert line breaks to <br> tags
    std::regex lineBreakRegex("\n");
    htmlContent = std::regex_replace(htmlContent, lineBreakRegex, "<br>\n");

    return htmlContent;
}

auto HtmlExporter::generateHtmlDocument(const std::string& bodyContent) const -> std::string {
    std::stringstream ss;

    ss << "<!DOCTYPE html>\n";
    ss << "<html lang=\"en\">\n";
    ss << "<head>\n";
    ss << generateMetaTags();
    ss << "  <title>" << escapeText(options_.title) << "</title>\n";
    ss << "  <style>\n" << generateCss() << "  </style>\n";

    // Include external CSS if specified
    if (!options_.cssPath.empty()) {
        auto externalCss = loadExternalCss(options_.cssPath);
        if (!externalCss.empty()) {
            ss << "  <style>\n" << externalCss << "  </style>\n";
        }
    }

    ss << "</head>\n";
    ss << "<body>\n";
    ss << generateHeader();
    ss << "  <main>\n";
    ss << bodyContent;
    ss << "  </main>\n";
    ss << generateFooter();
    ss << "  <script>\n" << generateJavaScript() << "  </script>\n";
    ss << "</body>\n";
    ss << "</html>\n";

    return ss.str();
}

auto HtmlExporter::generateCss() const -> std::string {
    return R"(
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
      color: #333;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    h1, h2, h3 {
      color: #2c3e50;
      border-bottom: 2px solid #3498db;
      padding-bottom: 10px;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      margin: 20px 0;
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    th, td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }

    th {
      background-color: #3498db;
      color: white;
      font-weight: 600;
    }

    tr:nth-child(even) {
      background-color: #f8f9fa;
    }

    tr:hover {
      background-color: #e8f4f8;
    }

    .timestamp {
      color: #7f8c8d;
      font-style: italic;
      margin-bottom: 20px;
    }

    .footer {
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid #ddd;
      text-align: center;
      color: #7f8c8d;
      font-size: 0.9em;
    }

    @media (max-width: 768px) {
      body { padding: 10px; }
      .container { padding: 15px; }
      table { font-size: 0.9em; }
      th, td { padding: 8px 10px; }
    }
)";
}

auto HtmlExporter::generateHeader() const -> std::string {
    std::stringstream ss;
    ss << "  <div class=\"container\">\n";
    ss << "    <header>\n";
    ss << "      <h1>" << escapeText(options_.title) << "</h1>\n";

    if (!options_.description.empty()) {
        ss << "      <p class=\"description\">" << escapeText(options_.description) << "</p>\n";
    }

    if (options_.includeTimestamp) {
        ss << "      <p class=\"timestamp\">Generated at: " << getCurrentTimestamp() << "</p>\n";
    }

    ss << "    </header>\n";

    return ss.str();
}

auto HtmlExporter::generateFooter() const -> std::string {
    std::stringstream ss;
    ss << "    <footer class=\"footer\">\n";
    ss << "      <p>Generated by Atom System Information Printer</p>\n";

    if (!options_.author.empty()) {
        ss << "      <p>Author: " << escapeText(options_.author) << "</p>\n";
    }

    ss << "    </footer>\n";
    ss << "  </div>\n";

    return ss.str();
}

auto HtmlExporter::convertTableToHtml(const std::string& tableContent) const -> std::string {
    // This is a simplified implementation
    // In a real implementation, you would parse ASCII tables and convert them to HTML tables
    std::string result = tableContent;

    // Convert table headers (lines with === or ---)
    std::regex headerRegex("=== (.+) ===");
    result = std::regex_replace(result, headerRegex, "<h3>$1</h3>");

    // Convert table rows (lines starting with |)
    std::regex rowRegex("\\| (.+) \\| (.+) \\|");
    result = std::regex_replace(result, rowRegex, "<tr><td>$1</td><td>$2</td></tr>");

    return result;
}

auto HtmlExporter::escapeText(const std::string& text) const -> std::string {
    std::string result = text;

    // Replace HTML special characters
    std::regex ampRegex("&");
    result = std::regex_replace(result, ampRegex, "&amp;");

    std::regex ltRegex("<");
    result = std::regex_replace(result, ltRegex, "&lt;");

    std::regex gtRegex(">");
    result = std::regex_replace(result, gtRegex, "&gt;");

    std::regex quotRegex("\"");
    result = std::regex_replace(result, quotRegex, "&quot;");

    std::regex aposRegex("'");
    result = std::regex_replace(result, aposRegex, "&#39;");

    return result;
}

auto HtmlExporter::addMetadata(const std::string& content) const -> std::string {
    if (!options_.includeMetadata) {
        return content;
    }

    // HTML metadata is added in the generateHtmlDocument method
    return content;
}

auto HtmlExporter::loadExternalCss(const std::string& cssPath) const -> std::string {
    return loadTemplate(cssPath);
}

auto HtmlExporter::generateMetaTags() const -> std::string {
    std::stringstream ss;
    ss << "  <meta charset=\"UTF-8\">\n";
    ss << "  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n";
    ss << "  <meta name=\"generator\" content=\"Atom System Information Printer\">\n";

    if (!options_.author.empty()) {
        ss << "  <meta name=\"author\" content=\"" << escapeText(options_.author) << "\">\n";
    }

    if (!options_.description.empty()) {
        ss << "  <meta name=\"description\" content=\"" << escapeText(options_.description) << "\">\n";
    }

    return ss.str();
}

auto HtmlExporter::generateJavaScript() const -> std::string {
    return R"(
    // Add interactive features
    document.addEventListener('DOMContentLoaded', function() {
      // Add click-to-copy functionality for table cells
      const cells = document.querySelectorAll('td');
      cells.forEach(cell => {
        cell.addEventListener('click', function() {
          navigator.clipboard.writeText(this.textContent).then(() => {
            this.style.backgroundColor = '#d4edda';
            setTimeout(() => {
              this.style.backgroundColor = '';
            }, 1000);
          });
        });
      });

      // Add search functionality
      const searchInput = document.createElement('input');
      searchInput.type = 'text';
      searchInput.placeholder = 'Search...';
      searchInput.style.cssText = 'margin: 10px 0; padding: 8px; width: 300px; border: 1px solid #ddd; border-radius: 4px;';

      const header = document.querySelector('header');
      if (header) {
        header.appendChild(searchInput);

        searchInput.addEventListener('input', function() {
          const searchTerm = this.value.toLowerCase();
          const rows = document.querySelectorAll('tr');

          rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
          });
        });
      }
    });
)";
}

} // namespace atom::system
