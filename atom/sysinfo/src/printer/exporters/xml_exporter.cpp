#include "xml_exporter.hpp"
#include <spdlog/spdlog.h>
#include <sstream>
#include <regex>

namespace atom::system {

XmlExporter::XmlExporter(const ExportOptions& options) {
    setOptions(options);
}

bool XmlExporter::exportToFile(const std::string& content, const std::string& filename) {
    auto xmlContent = exportToString(content);
    return writeToFile(xmlContent, filename);
}

auto XmlExporter::exportToString(const std::string& content) -> std::string {
    std::stringstream ss;

    ss << "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";

    if (options_.includeMetadata) {
        ss << "<!-- Generated by Atom System Information Printer -->\n";
        if (!options_.author.empty()) {
            ss << "<!-- Author: " << escapeText(options_.author) << " -->\n";
        }
        if (options_.includeTimestamp) {
            ss << "<!-- Generated at: " << getCurrentTimestamp() << " -->\n";
        }
    }

    ss << "<system_information>\n";

    if (options_.includeMetadata) {
        ss << "  <metadata>\n";
        ss << "    <title>" << escapeText(options_.title) << "</title>\n";
        ss << "    <generator>Atom System Information Printer</generator>\n";
        if (!options_.author.empty()) {
            ss << "    <author>" << escapeText(options_.author) << "</author>\n";
        }
        if (!options_.description.empty()) {
            ss << "    <description>" << escapeText(options_.description) << "</description>\n";
        }
        if (options_.includeTimestamp) {
            ss << "    <timestamp>" << getCurrentTimestamp() << "</timestamp>\n";
        }
        ss << "  </metadata>\n";
    }

    ss << "  <content>\n";
    ss << convertToXml(content);
    ss << "  </content>\n";
    ss << "</system_information>\n";

    return ss.str();
}

auto XmlExporter::getFileExtension() const -> std::string {
    return ".xml";
}

auto XmlExporter::getMimeType() const -> std::string {
    return "application/xml";
}

auto XmlExporter::convertToXml(const std::string& content) const -> std::string {
    // Simple XML conversion - wrap content in CDATA
    return "    <![CDATA[\n" + content + "\n    ]]>\n";
}

auto XmlExporter::escapeText(const std::string& text) const -> std::string {
    std::string result = text;

    // Escape XML special characters
    std::regex ampRegex("&");
    result = std::regex_replace(result, ampRegex, "&amp;");

    std::regex ltRegex("<");
    result = std::regex_replace(result, ltRegex, "&lt;");

    std::regex gtRegex(">");
    result = std::regex_replace(result, gtRegex, "&gt;");

    std::regex quotRegex("\"");
    result = std::regex_replace(result, quotRegex, "&quot;");

    std::regex aposRegex("'");
    result = std::regex_replace(result, aposRegex, "&apos;");

    return result;
}

} // namespace atom::system
