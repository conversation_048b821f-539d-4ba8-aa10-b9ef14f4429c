@PACKAGE_INIT@

include(CMakeFindDependencyMacro)

# Find required dependencies
find_dependency(spdlog REQUIRED)

# Include the targets file
include("${CMAKE_CURRENT_LIST_DIR}/atom_sysinfo_printer_targets.cmake")

# Set variables for compatibility
set(ATOM_SYSINFO_PRINTER_FOUND TRUE)
set(ATOM_SYSINFO_PRINTER_VERSION "@PROJECT_VERSION@")

# Check that all required components are available
check_required_components(atom_sysinfo_printer)
