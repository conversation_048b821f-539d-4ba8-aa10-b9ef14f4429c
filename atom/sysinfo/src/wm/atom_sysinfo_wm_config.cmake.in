@PACKAGE_INIT@

include(CMakeFindDependencyMacro)

# Find required dependencies
find_dependency(spdlog)

# Platform-specific dependencies
if(WIN32)
    # Windows dependencies are system libraries
elseif(UNIX AND NOT APPLE)
    # Linux - X11 is optional
    find_dependency(X11 QUIET)
elseif(APPLE)
    # macOS dependencies are system frameworks
endif()

# Include the targets file
include("${CMAKE_CURRENT_LIST_DIR}/atom_sysinfo_wm_targets.cmake")

check_required_components(atom_sysinfo_wm)
