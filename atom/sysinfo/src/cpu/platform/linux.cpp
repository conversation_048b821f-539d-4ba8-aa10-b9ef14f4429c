/*
 * linux.cpp
 *
 * Copyright (C) 2023-2024 Max <PERSON>an <lightapt.com>
 */

/*************************************************

Date: 2024-3-4

Description: System Information Module - CPU Linux Implementation
             Optimized with C++20 features, improved lock performance,
             and comprehensive spdlog logging

**************************************************/

#include <atomic>
#include <chrono>
#include <format>
#include <numeric>
#include <regex>
#include <shared_mutex>
#include <string_view>
#include <unordered_map>
#include <unordered_set>

#if defined(__linux__) || defined(__ANDROID__)

#include <spdlog/fmt/fmt.h>
#include <spdlog/spdlog.h>
#include "common.hpp"

namespace atom::system {

// Modern C++20 using declarations for better performance
using namespace std::string_view_literals;
using namespace std::chrono_literals;

// Thread-safe performance optimizations
namespace {
// Use shared_mutex for better read concurrency
inline std::shared_mutex g_cpu_usage_mutex;
inline std::shared_mutex g_temp_mutex;
inline std::shared_mutex g_freq_mutex;

// Atomic counters for better performance tracking
inline std::atomic<std::uint64_t> g_usage_calls{0};
inline std::atomic<std::uint64_t> g_temp_calls{0};

// Cached values with atomic updates
struct alignas(64) CpuUsageCache {  // Cache line aligned
    std::atomic<float> value{0.0f};
    std::atomic<std::chrono::steady_clock::time_point> last_update{};
    std::atomic<bool> valid{false};
};

inline CpuUsageCache g_cpu_usage_cache;
inline constexpr auto CACHE_DURATION = 100ms;  // More responsive caching
}  // namespace

// Forward declarations with C++20 attributes
[[nodiscard]] auto getCurrentCpuUsage_Linux() -> float;
[[nodiscard]] auto getPerCoreCpuUsage_Linux() -> std::vector<float>;
[[nodiscard]] auto getCurrentCpuTemperature_Linux() -> float;
[[nodiscard]] auto getPerCoreCpuTemperature_Linux() -> std::vector<float>;
[[nodiscard]] auto getCPUModel_Linux() -> std::string;

/*
 * IMPLEMENTATION NOTES:
 *
 * This Linux CPU implementation has been optimized with modern C++20 features:
 *
 * 1. PERFORMANCE OPTIMIZATIONS:
 *    - Thread-local storage for per-function statistics
 *    - Atomic caching with memory ordering for frequently accessed data
 *    - Shared mutexes for improved read concurrency
 *    - Cache line alignment for hot data structures
 *    - Lockless fast paths using atomics
 *
 * 2. MODERN C++ FEATURES:
 *    - C++20 attributes ([[likely]], [[unlikely]], [[nodiscard]])
 *    - Structured bindings for cleaner code
 *    - String view literals for zero-copy string operations
 *    - std::format for type-safe formatting
 *    - Constexpr arrays for compile-time optimizations
 *    - Move semantics and perfect forwarding
 *
 * 3. IMPROVED LOGGING:
 *    - Comprehensive spdlog integration
 *    - Debug, info, warn, and error levels
 *    - Performance metrics and timing information
 *    - Call counting for debugging
 *
 * 4. ERROR HANDLING:
 *    - Exception safety throughout
 *    - Graceful degradation on system call failures
 *    - Comprehensive input validation
 *    - Fallback mechanisms for missing kernel features
 *
 * 5. MEMORY EFFICIENCY:
 *    - Static caching to avoid repeated allocations
 *    - Vector reserve() calls for predictable sizes
 *    - Unordered containers for O(1) operations where appropriate
 *    - Minimal memory footprint for cache structures
 */

auto getCurrentCpuUsage_Linux() -> float {
    const auto call_id = ++g_usage_calls;
    spdlog::debug("getCurrentCpuUsage_Linux called (call #{})", call_id);

    // Fast path: check atomic cache first (lockless)
    const auto now = std::chrono::steady_clock::now();
    if (g_cpu_usage_cache.valid.load(std::memory_order_acquire)) {
        const auto last_update =
            g_cpu_usage_cache.last_update.load(std::memory_order_acquire);
        if (now - last_update < CACHE_DURATION) {
            const auto cached_value =
                g_cpu_usage_cache.value.load(std::memory_order_acquire);
            spdlog::debug("Using cached CPU usage: {:.2f}% (age: {}ms)",
                          cached_value,
                          std::chrono::duration_cast<std::chrono::milliseconds>(
                              now - last_update)
                              .count());
            return cached_value;
        }
    }

    // Slow path: need to read from /proc/stat
    static thread_local struct {
        alignas(64) std::uint64_t lastTotalUser{0};
        alignas(64) std::uint64_t lastTotalUserLow{0};
        alignas(64) std::uint64_t lastTotalSys{0};
        alignas(64) std::uint64_t lastTotalIdle{0};
        alignas(64) std::chrono::steady_clock::time_point lastMeasurement{};
    } tl_stats;

    auto cpuUsage = 0.0f;

    // Use shared_lock for reading (allows multiple readers)
    {
        std::unique_lock lock(g_cpu_usage_mutex);

        try {
            std::ifstream statFile("/proc/stat");
            if (!statFile.is_open()) [[unlikely]] {
                spdlog::error("Failed to open /proc/stat");
                return 0.0f;
            }

            std::string line;
            if (!std::getline(statFile, line)) [[unlikely]] {
                spdlog::error("Failed to read first line from /proc/stat");
                return 0.0f;
            }

            std::istringstream ss(line);
            std::string cpu_label;
            std::array<std::uint64_t, 8> cpu_times{};

            // Modern structured reading
            if (!(ss >> cpu_label >> cpu_times[0] >> cpu_times[1] >>
                  cpu_times[2] >> cpu_times[3] >> cpu_times[4] >>
                  cpu_times[5] >> cpu_times[6] >> cpu_times[7])) [[unlikely]] {
                spdlog::error("Failed to parse CPU statistics from /proc/stat");
                return 0.0f;
            }

            if (cpu_label != "cpu"sv) [[unlikely]] {
                spdlog::error("Unexpected CPU label: {}", cpu_label);
                return 0.0f;
            }

            // Extract values with meaningful names
            const auto [user, nice, system, idle, iowait, irq, softirq, steal] =
                cpu_times;

            const auto totalUser = user + nice;
            const auto totalSys = system + irq + softirq;
            const auto totalIdle = idle + iowait;
            const auto total = totalUser + totalSys + totalIdle + steal;

            // Calculate delta with overflow protection
            if (tl_stats.lastTotalUser > 0) [[likely]] {
                const auto totalDelta =
                    total -
                    (tl_stats.lastTotalUser + tl_stats.lastTotalUserLow +
                     tl_stats.lastTotalSys + tl_stats.lastTotalIdle);

                if (totalDelta > 0) [[likely]] {
                    const auto idleDelta = totalIdle - tl_stats.lastTotalIdle;
                    cpuUsage =
                        100.0f * (1.0f - static_cast<float>(idleDelta) /
                                             static_cast<float>(totalDelta));
                }
            }

            // Update thread-local cache
            tl_stats.lastTotalUser = totalUser;
            tl_stats.lastTotalUserLow = totalUser;  // Keep for compatibility
            tl_stats.lastTotalSys = totalSys;
            tl_stats.lastTotalIdle = totalIdle;
            tl_stats.lastMeasurement = now;

        } catch (const std::exception& e) {
            spdlog::error("Exception in getCurrentCpuUsage_Linux: {}",
                          e.what());
            return 0.0f;
        }
    }

    // Clamp and validate result
    cpuUsage = std::clamp(cpuUsage, 0.0f, 100.0f);

    // Update atomic cache (lockless)
    g_cpu_usage_cache.value.store(cpuUsage, std::memory_order_release);
    g_cpu_usage_cache.last_update.store(now, std::memory_order_release);
    g_cpu_usage_cache.valid.store(true, std::memory_order_release);

    spdlog::info("Linux CPU Usage: {:.2f}% (call #{})", cpuUsage, call_id);
    return cpuUsage;
}

auto getPerCoreCpuUsage() -> std::vector<float> {
    spdlog::debug(
        "getPerCoreCpuUsage_Linux: Starting per-core CPU usage collection");

    // Use thread-local storage for per-core statistics (better performance)
    static thread_local struct {
        std::vector<std::uint64_t> lastTotalUser;
        std::vector<std::uint64_t> lastTotalUserLow;
        std::vector<std::uint64_t> lastTotalSys;
        std::vector<std::uint64_t> lastTotalIdle;
        std::chrono::steady_clock::time_point lastUpdate{};
    } tl_core_stats;

    std::vector<float> coreUsages;
    coreUsages.reserve(16);  // Reserve space for typical CPU count

    // Use shared_lock for better concurrency
    std::shared_lock lock(g_cpu_usage_mutex);

    try {
        std::ifstream statFile("/proc/stat");
        if (!statFile.is_open()) [[unlikely]] {
            spdlog::error("Failed to open /proc/stat for per-core usage");
            return {};
        }

        std::string line;
        // Skip the first line (overall CPU usage)
        if (!std::getline(statFile, line)) [[unlikely]] {
            spdlog::error("Failed to read first line from /proc/stat");
            return {};
        }

        auto coreIndex = 0;
        while (std::getline(statFile, line)) {
            if (!line.starts_with("cpu"sv)) [[unlikely]] {
                break;  // We've processed all CPU entries
            }

            std::istringstream ss(line);
            std::string cpu_label;
            std::array<std::uint64_t, 8> cpu_times{};

            if (!(ss >> cpu_label >> cpu_times[0] >> cpu_times[1] >>
                  cpu_times[2] >> cpu_times[3] >> cpu_times[4] >>
                  cpu_times[5] >> cpu_times[6] >> cpu_times[7])) [[unlikely]] {
                spdlog::warn("Failed to parse CPU statistics for core {}",
                             coreIndex);
                continue;
            }

            // Resize vectors if needed (reserve more space for efficiency)
            if (coreIndex >=
                static_cast<int>(tl_core_stats.lastTotalUser.size())) {
                const auto new_size =
                    std::max(static_cast<size_t>(coreIndex + 1),
                             tl_core_stats.lastTotalUser.size() * 2);
                tl_core_stats.lastTotalUser.resize(new_size, 0);
                tl_core_stats.lastTotalUserLow.resize(new_size, 0);
                tl_core_stats.lastTotalSys.resize(new_size, 0);
                tl_core_stats.lastTotalIdle.resize(new_size, 0);
            }

            // Extract values with meaningful names
            const auto [user, nice, system, idle, iowait, irq, softirq, steal] =
                cpu_times;

            const auto totalUser = user + nice;
            const auto totalSys = system + irq + softirq;
            const auto totalIdle = idle + iowait;
            const auto total = totalUser + totalSys + totalIdle + steal;

            auto coreUsage = 0.0f;

            // Calculate the delta between current and last measurement
            if (tl_core_stats.lastTotalUser[coreIndex] > 0) [[likely]] {
                const auto totalDelta =
                    total - (tl_core_stats.lastTotalUser[coreIndex] +
                             tl_core_stats.lastTotalUserLow[coreIndex] +
                             tl_core_stats.lastTotalSys[coreIndex] +
                             tl_core_stats.lastTotalIdle[coreIndex]);

                if (totalDelta > 0) [[likely]] {
                    const auto idleDelta =
                        totalIdle - tl_core_stats.lastTotalIdle[coreIndex];
                    coreUsage =
                        100.0f * (1.0f - static_cast<float>(idleDelta) /
                                             static_cast<float>(totalDelta));
                }
            }

            // Store the current values for the next calculation
            tl_core_stats.lastTotalUser[coreIndex] = totalUser;
            tl_core_stats.lastTotalUserLow[coreIndex] = totalUser;
            tl_core_stats.lastTotalSys[coreIndex] = totalSys;
            tl_core_stats.lastTotalIdle[coreIndex] = totalIdle;

            // Clamp to 0-100 range
            coreUsage = std::clamp(coreUsage, 0.0f, 100.0f);
            coreUsages.push_back(coreUsage);

            ++coreIndex;
        }

        tl_core_stats.lastUpdate = std::chrono::steady_clock::now();

    } catch (const std::exception& e) {
        spdlog::error("Exception in getPerCoreCpuUsage: {}", e.what());
        return {};
    }

    spdlog::info(
        "Linux Per-Core CPU Usage collected for {} cores (avg: {:.2f}%)",
        coreUsages.size(),
        coreUsages.empty()
            ? 0.0f
            : std::accumulate(coreUsages.begin(), coreUsages.end(), 0.0f) /
                  coreUsages.size());

    return coreUsages;
}

auto getCurrentCpuTemperature() -> float {
    const auto call_id = ++g_temp_calls;
    spdlog::debug("getCurrentCpuTemperature_Linux called (call #{})", call_id);

    // Cache for temperature readings (since temperature changes slowly)
    static std::atomic<float> cached_temp{0.0f};
    static std::atomic<std::chrono::steady_clock::time_point> last_temp_read{};
    constexpr auto TEMP_CACHE_DURATION = 1s;  // Temperature cache for 1 second

    const auto now = std::chrono::steady_clock::now();
    const auto last_read = last_temp_read.load(std::memory_order_acquire);
    if (now - last_read < TEMP_CACHE_DURATION &&
        cached_temp.load(std::memory_order_acquire) > 0.0f) {
        const auto temp = cached_temp.load(std::memory_order_acquire);
        spdlog::debug("Using cached CPU temperature: {:.1f}°C", temp);
        return temp;
    }

    auto temperature = 0.0f;
    auto found = false;

    std::shared_lock lock(g_temp_mutex);

    try {
        // Modern approach: use structured bindings and ranges
        constexpr std::array thermal_paths = {
            "/sys/class/thermal/thermal_zone0/temp"sv,
            "/sys/class/thermal/thermal_zone1/temp"sv,
            "/sys/class/thermal/thermal_zone2/temp"sv,
            "/sys/class/thermal/thermal_zone3/temp"sv,
            "/sys/class/thermal/thermal_zone4/temp"sv};

        // Check thermal zones first (most common)
        for (const auto& path : thermal_paths) {
            std::ifstream tempFile(path.data());
            if (!tempFile.is_open())
                continue;

            std::string line;
            if (std::getline(tempFile, line)) {
                try {
                    // Temperature is often reported in millidegrees Celsius
                    temperature = static_cast<float>(std::stoi(line)) / 1000.0f;
                    found = true;
                    spdlog::debug("Found CPU temperature from {}: {:.1f}°C",
                                  path, temperature);
                    break;
                } catch (const std::exception& e) {
                    spdlog::debug("Error parsing temperature from {}: {}", path,
                                  e.what());
                }
            }
        }

        // Check hwmon sensors if thermal zones didn't work
        if (!found) [[unlikely]] {
            constexpr std::string_view hwmon_base = "/sys/class/hwmon/"sv;
            constexpr std::array sensor_names = {"coretemp"sv, "k10temp"sv,
                                                 "cpu_thermal"sv};

            for (int i = 0; i < 10 && !found; ++i) {
                const auto hwmon_path =
                    std::string{hwmon_base} + "hwmon" + std::to_string(i) + "/";

                // Check if this is a CPU temperature sensor
                std::ifstream nameFile(hwmon_path + "name");
                if (!nameFile.is_open())
                    continue;

                std::string name;
                if (!std::getline(nameFile, name))
                    continue;

                // Check if this sensor is relevant for CPU temperature
                const auto is_cpu_sensor = std::any_of(
                    sensor_names.begin(), sensor_names.end(),
                    [&name](const auto& sensor_name) {
                        return name.find(sensor_name) != std::string::npos;
                    });

                if (!is_cpu_sensor)
                    continue;

                // Try to read temperature from this hwmon device
                for (int j = 1; j < 5 && !found; ++j) {
                    const auto temp_path =
                        hwmon_path + "temp" + std::to_string(j) + "_input";
                    std::ifstream tempFile(temp_path);

                    if (!tempFile.is_open())
                        continue;

                    std::string line;
                    if (std::getline(tempFile, line)) {
                        try {
                            temperature =
                                static_cast<float>(std::stoi(line)) / 1000.0f;
                            found = true;
                            spdlog::debug(
                                "Found CPU temperature from {}: {:.1f}°C",
                                temp_path, temperature);
                        } catch (const std::exception& e) {
                            spdlog::debug(
                                "Error parsing temperature from {}: {}",
                                temp_path, e.what());
                        }
                    }
                }
            }
        }

    } catch (const std::exception& e) {
        spdlog::error("Exception in getCurrentCpuTemperature: {}", e.what());
        return 0.0f;
    }

    if (!found) [[unlikely]] {
        spdlog::warn("Could not find CPU temperature sensors, returning 0°C");
        temperature = 0.0f;
    }

    // Validate temperature range (reasonable for CPUs)
    if (temperature < -10.0f || temperature > 120.0f) [[unlikely]] {
        spdlog::warn("CPU temperature {:.1f}°C seems unreasonable, clamping",
                     temperature);
        temperature = std::clamp(temperature, 0.0f, 100.0f);
    }

    // Update cache
    cached_temp.store(temperature, std::memory_order_release);
    last_temp_read.store(now, std::memory_order_release);

    spdlog::info("Linux CPU Temperature: {:.1f}°C (call #{})", temperature,
                 call_id);
    return temperature;
}

auto getPerCoreCpuTemperature() -> std::vector<float> {
    spdlog::info("Starting getPerCoreCpuTemperature function on Linux");

    std::vector<float> temperatures;
    bool found = false;

    // Try to find per-core temperatures in hwmon
    std::string hwmonDir = "/sys/class/hwmon/";

    for (int i = 0; i < 10 && !found; i++) {
        std::string hwmonPath = hwmonDir + "hwmon" + std::to_string(i) + "/";

        // Check if this is a CPU temperature sensor
        std::ifstream nameFile(hwmonPath + "name");
        if (nameFile.is_open()) {
            std::string name;
            if (std::getline(nameFile, name)) {
                // Common CPU temperature sensor names
                if (name.find("coretemp") != std::string::npos ||
                    name.find("k10temp") != std::string::npos) {
                    // Find all temperature inputs
                    std::vector<std::string> tempPaths;
                    for (int j = 1; j < 32; j++) {
                        std::string labelPath =
                            hwmonPath + "temp" + std::to_string(j) + "_label";
                        std::ifstream labelFile(labelPath);

                        if (labelFile.is_open()) {
                            std::string label;
                            if (std::getline(labelFile, label)) {
                                // Check if this is a core temperature
                                if (label.find("Core") != std::string::npos ||
                                    label.find("CPU") != std::string::npos) {
                                    tempPaths.push_back(hwmonPath + "temp" +
                                                        std::to_string(j) +
                                                        "_input");
                                }
                            }
                        }
                    }

                    // Read each core temperature
                    if (!tempPaths.empty()) {
                        found = true;

                        for (const auto& path : tempPaths) {
                            std::ifstream tempFile(path);
                            float temp = 0.0f;

                            if (tempFile.is_open()) {
                                std::string line;
                                if (std::getline(tempFile, line)) {
                                    try {
                                        // Temperature is often reported in
                                        // millidegrees Celsius
                                        temp = static_cast<float>(
                                                   std::stoi(line)) /
                                               1000.0f;
                                        spdlog::info(
                                            "Found core temperature from {}: "
                                            "{}°C",
                                            path, temp);
                                    } catch (const std::exception& e) {
                                        spdlog::warn(
                                            "Error parsing temperature from "
                                            "{}: {}",
                                            path, e.what());
                                    }
                                }
                            }

                            temperatures.push_back(temp);
                        }
                    }
                }
            }
        }
    }

    // If we couldn't find per-core temperatures, fall back to single
    // temperature
    if (!found) {
        float coreTemp = getCurrentCpuTemperature();
        int numCores = getNumberOfLogicalCores();

        temperatures.resize(numCores, coreTemp);
        spdlog::info(
            "Could not find per-core temperatures, using single temperature "
            "for all cores: {}°C",
            coreTemp);
    }

    spdlog::info("Linux Per-Core CPU Temperature collected for {} cores",
                 temperatures.size());
    return temperatures;
}

auto getCPUModel() -> std::string {
    spdlog::debug("getCPUModel_Linux: Retrieving CPU model information");

    // Use atomic caching for CPU model (rarely changes)
    static std::atomic<bool> model_cached{false};
    static std::string cached_model;
    static std::shared_mutex model_cache_mutex;

    // Fast path: return cached result
    {
        std::shared_lock lock(model_cache_mutex);
        if (model_cached.load(std::memory_order_acquire) &&
            !cached_model.empty()) {
            spdlog::debug("Using cached CPU model: {}", cached_model);
            return cached_model;
        }
    }

    // Slow path: read from /proc/cpuinfo
    std::string cpuModel = "Unknown";

    try {
        std::ifstream cpuinfo("/proc/cpuinfo");
        if (!cpuinfo.is_open()) [[unlikely]] {
            spdlog::error("Failed to open /proc/cpuinfo");
            return cpuModel;
        }

        // Modern approach: use string_view for pattern matching
        constexpr std::array model_patterns = {
            "model name"sv, "Processor"sv, "cpu model"sv,
            "Hardware"sv  // Hardware for ARM
        };

        std::string line;
        while (std::getline(cpuinfo, line)) {
            // Check if line contains any of our patterns
            const auto found_pattern =
                std::any_of(model_patterns.begin(), model_patterns.end(),
                            [&line](const auto& pattern) {
                                return line.find(pattern) != std::string::npos;
                            });

            if (found_pattern) {
                if (const auto pos = line.find(':');
                    pos != std::string::npos && pos + 2 < line.size()) {
                    cpuModel = line.substr(pos + 2);

                    // Trim whitespace using modern C++
                    if (const auto start =
                            cpuModel.find_first_not_of(" \t\r\n");
                        start != std::string::npos) {
                        cpuModel = cpuModel.substr(start);
                        if (const auto end =
                                cpuModel.find_last_not_of(" \t\r\n");
                            end != std::string::npos) {
                            cpuModel = cpuModel.substr(0, end + 1);
                        }
                    }
                    break;
                }
            }
        }

        // Cache the result
        {
            std::unique_lock lock(model_cache_mutex);
            cached_model = cpuModel;
            model_cached.store(true, std::memory_order_release);
        }

    } catch (const std::exception& e) {
        spdlog::error("Exception in getCPUModel: {}", e.what());
        return "Unknown";
    }

    spdlog::info("Linux CPU Model: {}", cpuModel);
    return cpuModel;
}

auto getProcessorIdentifier() -> std::string {
    spdlog::debug(
        "getProcessorIdentifier_Linux: Building processor identifier");

    // Use atomic caching
    static std::atomic<bool> identifier_cached{false};
    static std::string cached_identifier;
    static std::shared_mutex identifier_cache_mutex;

    // Fast path: return cached result
    {
        std::shared_lock lock(identifier_cache_mutex);
        if (identifier_cached.load(std::memory_order_acquire) &&
            !cached_identifier.empty()) {
            spdlog::debug("Using cached processor identifier: {}",
                          cached_identifier);
            return cached_identifier;
        }
    }

    // Slow path: build identifier
    std::string identifier;

    try {
        // Use structured data collection
        struct CpuIdentifierData {
            std::string vendor;
            std::string family;
            std::string model;
            std::string stepping;
        } cpu_data;

        std::ifstream cpuinfo("/proc/cpuinfo");
        if (!cpuinfo.is_open()) [[unlikely]] {
            spdlog::error(
                "Failed to open /proc/cpuinfo for processor identifier");
            return getCPUModel();
        }

        // Map of field names to their target locations
        const std::unordered_map<std::string_view, std::string*> field_map = {
            {"vendor_id"sv, &cpu_data.vendor},
            {"cpu family"sv, &cpu_data.family},
            {"model"sv, &cpu_data.model},
            {"stepping"sv, &cpu_data.stepping}};

        std::string line;
        while (std::getline(cpuinfo, line)) {
            // Skip model name to avoid confusion with model number
            if (line.find("model name") != std::string::npos)
                continue;

            for (const auto& [pattern, target] : field_map) {
                if (line.find(pattern) != std::string::npos) {
                    if (const auto pos = line.find(':');
                        pos != std::string::npos && pos + 2 < line.size()) {
                        *target = line.substr(pos + 2);

                        // Trim whitespace
                        if (const auto start =
                                target->find_first_not_of(" \t\r\n");
                            start != std::string::npos) {
                            *target = target->substr(start);
                            if (const auto end =
                                    target->find_last_not_of(" \t\r\n");
                                end != std::string::npos) {
                                *target = target->substr(0, end + 1);
                            }
                        }
                    }
                    break;
                }
            }
        }

        // Build identifier string
        if (!cpu_data.vendor.empty() && !cpu_data.family.empty() &&
            !cpu_data.model.empty() && !cpu_data.stepping.empty()) {
            identifier = std::format("{} Family {} Model {} Stepping {}",
                                     cpu_data.vendor, cpu_data.family,
                                     cpu_data.model, cpu_data.stepping);
        } else {
            identifier = getCPUModel();  // Fallback to CPU model
        }

        // Cache the result
        {
            std::unique_lock lock(identifier_cache_mutex);
            cached_identifier = identifier;
            identifier_cached.store(true, std::memory_order_release);
        }

    } catch (const std::exception& e) {
        spdlog::error("Exception in getProcessorIdentifier: {}", e.what());
        return getCPUModel();
    }

    spdlog::info("Linux CPU Identifier: {}", identifier);
    return identifier;
}

auto getProcessorFrequency() -> double {
    spdlog::debug("getProcessorFrequency_Linux: Reading current CPU frequency");

    // Cache for frequency (changes less frequently than usage)
    static std::atomic<double> cached_frequency{0.0};
    static std::atomic<std::chrono::steady_clock::time_point> last_freq_read{};
    constexpr auto FREQ_CACHE_DURATION = 2s;

    const auto now = std::chrono::steady_clock::now();
    const auto last_read = last_freq_read.load(std::memory_order_acquire);
    if (now - last_read < FREQ_CACHE_DURATION &&
        cached_frequency.load(std::memory_order_acquire) > 0.0) {
        const auto freq = cached_frequency.load(std::memory_order_acquire);
        spdlog::debug("Using cached processor frequency: {:.3f} GHz", freq);
        return freq;
    }

    auto frequency = 0.0;

    std::shared_lock lock(g_freq_mutex);

    try {
        // Priority order: scaling_cur_freq -> cpuinfo -> fallback
        constexpr std::array freq_paths = {
            "/sys/devices/system/cpu/cpu0/cpufreq/scaling_cur_freq"sv,
            "/sys/devices/system/cpu/cpu0/cpufreq/cpuinfo_cur_freq"sv};

        // Try sysfs first (more accurate for current frequency)
        for (const auto& path : freq_paths) {
            std::ifstream freqFile(path.data());
            if (!freqFile.is_open())
                continue;

            std::string line;
            if (std::getline(freqFile, line)) {
                try {
                    // Convert kHz to GHz
                    frequency = std::stod(line) / 1'000'000.0;
                    spdlog::debug("Found CPU frequency from {}: {:.3f} GHz",
                                  path, frequency);
                    break;
                } catch (const std::exception& e) {
                    spdlog::debug("Error parsing frequency from {}: {}", path,
                                  e.what());
                }
            }
        }

        // Fallback to /proc/cpuinfo if sysfs didn't work
        if (frequency <= 0.0) [[unlikely]] {
            std::ifstream cpuinfo("/proc/cpuinfo");
            if (cpuinfo.is_open()) {
                std::string line;
                while (std::getline(cpuinfo, line)) {
                    if (line.find("cpu MHz") != std::string::npos ||
                        line.find("clock") != std::string::npos) {
                        if (const auto pos = line.find(':');
                            pos != std::string::npos && pos + 2 < line.size()) {
                            const auto freqStr = line.substr(pos + 2);
                            try {
                                // Convert MHz to GHz
                                frequency = std::stod(freqStr) / 1000.0;
                                spdlog::debug(
                                    "Found CPU frequency from /proc/cpuinfo: "
                                    "{:.3f} GHz",
                                    frequency);
                                break;
                            } catch (const std::exception& e) {
                                spdlog::debug(
                                    "Error parsing CPU frequency from cpuinfo: "
                                    "{}",
                                    e.what());
                            }
                        }
                    }
                }
            }
        }

        // Update cache
        if (frequency > 0.0) {
            cached_frequency.store(frequency, std::memory_order_release);
            last_freq_read.store(now, std::memory_order_release);
        }

    } catch (const std::exception& e) {
        spdlog::error("Exception in getProcessorFrequency: {}", e.what());
        return 0.0;
    }

    if (frequency <= 0.0) [[unlikely]] {
        spdlog::warn("Could not determine CPU frequency, returning 0");
    }

    spdlog::info("Linux CPU Frequency: {:.3f} GHz", frequency);
    return frequency;
}

auto getMinProcessorFrequency() -> double {
    spdlog::debug(
        "getMinProcessorFrequency_Linux: Reading minimum CPU frequency");

    // Static cache for min frequency (hardware limit, never changes)
    static std::atomic<double> cached_min_freq{0.0};
    if (const auto cached = cached_min_freq.load(std::memory_order_acquire);
        cached > 0.0) {
        spdlog::debug("Using cached min processor frequency: {:.3f} GHz",
                      cached);
        return cached;
    }

    auto minFreq = 0.0;

    try {
        // Try sysfs paths in priority order
        constexpr std::array min_freq_paths = {
            "/sys/devices/system/cpu/cpu0/cpufreq/scaling_min_freq"sv,
            "/sys/devices/system/cpu/cpu0/cpufreq/cpuinfo_min_freq"sv};

        for (const auto& path : min_freq_paths) {
            std::ifstream freqFile(path.data());
            if (!freqFile.is_open())
                continue;

            std::string line;
            if (std::getline(freqFile, line)) {
                try {
                    // Convert kHz to GHz
                    minFreq = std::stod(line) / 1'000'000.0;
                    spdlog::debug("Found min CPU frequency from {}: {:.3f} GHz",
                                  path, minFreq);
                    break;
                } catch (const std::exception& e) {
                    spdlog::debug("Error parsing min frequency from {}: {}",
                                  path, e.what());
                }
            }
        }

        // Fallback: estimate from current frequency
        if (minFreq <= 0.0) [[unlikely]] {
            const auto currentFreq = getProcessorFrequency();
            minFreq = currentFreq > 0.0 ? currentFreq * 0.3
                                        : 1.0;  // Assume min is 30% of current
            spdlog::debug("Estimated min CPU frequency: {:.3f} GHz", minFreq);
        }

        // Cache the result
        cached_min_freq.store(minFreq, std::memory_order_release);

    } catch (const std::exception& e) {
        spdlog::error("Exception in getMinProcessorFrequency: {}", e.what());
        return 1.0;  // Safe fallback
    }

    spdlog::info("Linux CPU Min Frequency: {:.3f} GHz", minFreq);
    return minFreq;
}

auto getMaxProcessorFrequency() -> double {
    spdlog::debug(
        "getMaxProcessorFrequency_Linux: Reading maximum CPU frequency");

    // Static cache for max frequency (hardware limit, never changes)
    static std::atomic<double> cached_max_freq{0.0};
    if (const auto cached = cached_max_freq.load(std::memory_order_acquire);
        cached > 0.0) {
        spdlog::debug("Using cached max processor frequency: {:.3f} GHz",
                      cached);
        return cached;
    }

    auto maxFreq = 0.0;

    try {
        // Try sysfs paths in priority order
        constexpr std::array max_freq_paths = {
            "/sys/devices/system/cpu/cpu0/cpufreq/scaling_max_freq"sv,
            "/sys/devices/system/cpu/cpu0/cpufreq/cpuinfo_max_freq"sv};

        for (const auto& path : max_freq_paths) {
            std::ifstream freqFile(path.data());
            if (!freqFile.is_open())
                continue;

            std::string line;
            if (std::getline(freqFile, line)) {
                try {
                    // Convert kHz to GHz
                    maxFreq = std::stod(line) / 1'000'000.0;
                    spdlog::debug("Found max CPU frequency from {}: {:.3f} GHz",
                                  path, maxFreq);
                    break;
                } catch (const std::exception& e) {
                    spdlog::debug("Error parsing max frequency from {}: {}",
                                  path, e.what());
                }
            }
        }

        // Fallback to current frequency
        if (maxFreq <= 0.0) [[unlikely]] {
            maxFreq = getProcessorFrequency();
            spdlog::warn(
                "Could not determine max CPU frequency, using current: {:.3f} "
                "GHz",
                maxFreq);
        }

        // Cache the result
        cached_max_freq.store(maxFreq, std::memory_order_release);

    } catch (const std::exception& e) {
        spdlog::error("Exception in getMaxProcessorFrequency: {}", e.what());
        return getProcessorFrequency();  // Fallback to current
    }

    spdlog::info("Linux CPU Max Frequency: {:.3f} GHz", maxFreq);
    return maxFreq;
}

auto getPerCoreFrequencies() -> std::vector<double> {
    spdlog::debug("getPerCoreFrequencies_Linux: Reading per-core frequencies");

    const auto numCores = getNumberOfLogicalCores();
    std::vector<double> frequencies;
    frequencies.reserve(numCores);

    try {
        const auto globalFreq = getProcessorFrequency();  // Fallback value

        for (int i = 0; i < numCores; ++i) {
            const auto freqPath = std::format(
                "/sys/devices/system/cpu/cpu{}/cpufreq/scaling_cur_freq", i);
            std::ifstream freqFile(freqPath);

            auto coreFreq = 0.0;
            if (freqFile.is_open()) {
                std::string line;
                if (std::getline(freqFile, line)) {
                    try {
                        // Convert kHz to GHz
                        coreFreq = std::stod(line) / 1'000'000.0;
                    } catch (const std::exception& e) {
                        spdlog::debug("Error parsing frequency for core {}: {}",
                                      i, e.what());
                    }
                }
            }

            // Use global frequency as fallback
            if (coreFreq <= 0.0) {
                coreFreq = (i == 0)              ? globalFreq
                           : frequencies.empty() ? globalFreq
                                                 : frequencies[0];
            }

            frequencies.push_back(coreFreq);
        }

    } catch (const std::exception& e) {
        spdlog::error("Exception in getPerCoreFrequencies: {}", e.what());
        return std::vector<double>(numCores, 1.0);  // Safe fallback
    }

    spdlog::info("Linux Per-Core CPU Frequencies: {} cores, avg {:.3f} GHz",
                 frequencies.size(),
                 frequencies.empty() ? 0.0
                                     : std::accumulate(frequencies.begin(),
                                                       frequencies.end(), 0.0) /
                                           frequencies.size());

    return frequencies;
}

auto getNumberOfPhysicalPackages() -> int {
    spdlog::debug("getNumberOfPhysicalPackages_Linux: Counting CPU packages");

    // Use static cache for package count (hardware topology doesn't change)
    static std::atomic<int> cached_packages{0};
    if (const auto cached = cached_packages.load(std::memory_order_acquire);
        cached > 0) {
        spdlog::debug("Using cached physical package count: {}", cached);
        return cached;
    }

    auto numberOfPackages = 0;

    try {
        std::unordered_set<std::string>
            physicalIds;  // Use unordered_set for O(1) operations

        std::ifstream cpuinfo("/proc/cpuinfo");
        if (!cpuinfo.is_open()) [[unlikely]] {
            spdlog::warn("Failed to open /proc/cpuinfo");
            numberOfPackages = 1;  // Assume at least one package
        } else {
            std::string line;
            while (std::getline(cpuinfo, line)) {
                if (line.find("physical id") != std::string::npos) {
                    if (const auto pos = line.find(':');
                        pos != std::string::npos && pos + 2 < line.size()) {
                        auto physical_id = line.substr(pos + 2);

                        // Trim whitespace
                        if (const auto start =
                                physical_id.find_first_not_of(" \t\r\n");
                            start != std::string::npos) {
                            physical_id = physical_id.substr(start);
                            if (const auto end =
                                    physical_id.find_last_not_of(" \t\r\n");
                                end != std::string::npos) {
                                physical_id = physical_id.substr(0, end + 1);
                            }
                        }

                        physicalIds.insert(physical_id);
                    }
                }
            }

            numberOfPackages = static_cast<int>(physicalIds.size());
        }

        // Ensure at least one package
        if (numberOfPackages <= 0) {
            numberOfPackages = 1;
            spdlog::warn(
                "Could not determine number of physical CPU packages, assuming "
                "1");
        }

        // Cache the result
        cached_packages.store(numberOfPackages, std::memory_order_release);

    } catch (const std::exception& e) {
        spdlog::error("Exception in getNumberOfPhysicalPackages: {}", e.what());
        return 1;
    }

    spdlog::info("Linux Physical CPU Packages: {}", numberOfPackages);
    return numberOfPackages;
}

auto getNumberOfPhysicalCores() -> int {
    spdlog::debug(
        "getNumberOfPhysicalCores_Linux: Counting physical CPU cores");

    // Use static cache for core count (hardware topology doesn't change)
    static std::atomic<int> cached_cores{0};
    if (const auto cached = cached_cores.load(std::memory_order_acquire);
        cached > 0) {
        spdlog::debug("Using cached physical core count: {}", cached);
        return cached;
    }

    auto numberOfCores = 0;

    try {
        // Modern approach: use unordered containers for better performance
        std::unordered_map<std::string, std::unordered_set<std::string>>
            coresPerPackage;

        std::ifstream cpuinfo("/proc/cpuinfo");
        if (!cpuinfo.is_open()) [[unlikely]] {
            spdlog::warn("Failed to open /proc/cpuinfo for physical cores");
            numberOfCores = getNumberOfLogicalCores();
        } else {
            std::string line;
            std::string currentPhysicalId;

            while (std::getline(cpuinfo, line)) {
                if (line.find("physical id") != std::string::npos) {
                    if (const auto pos = line.find(':');
                        pos != std::string::npos && pos + 2 < line.size()) {
                        currentPhysicalId = line.substr(pos + 2);

                        // Trim whitespace
                        if (const auto start =
                                currentPhysicalId.find_first_not_of(" \t\r\n");
                            start != std::string::npos) {
                            currentPhysicalId = currentPhysicalId.substr(start);
                            if (const auto end =
                                    currentPhysicalId.find_last_not_of(
                                        " \t\r\n");
                                end != std::string::npos) {
                                currentPhysicalId =
                                    currentPhysicalId.substr(0, end + 1);
                            }
                        }
                    }
                } else if (line.find("core id") != std::string::npos &&
                           !currentPhysicalId.empty()) {
                    if (const auto pos = line.find(':');
                        pos != std::string::npos && pos + 2 < line.size()) {
                        auto coreId = line.substr(pos + 2);

                        // Trim whitespace
                        if (const auto start =
                                coreId.find_first_not_of(" \t\r\n");
                            start != std::string::npos) {
                            coreId = coreId.substr(start);
                            if (const auto end =
                                    coreId.find_last_not_of(" \t\r\n");
                                end != std::string::npos) {
                                coreId = coreId.substr(0, end + 1);
                            }
                        }

                        coresPerPackage[currentPhysicalId].insert(coreId);
                    }
                }
            }

            // Count unique cores across all packages
            numberOfCores = std::accumulate(
                coresPerPackage.begin(), coresPerPackage.end(), 0,
                [](int sum, const auto& package) {
                    return sum + static_cast<int>(package.second.size());
                });
        }

        // Alternative approach if core_id method didn't work
        if (numberOfCores <= 0) [[unlikely]] {
            spdlog::debug(
                "Trying alternative approach using 'cpu cores' field");

            std::ifstream cpuinfo2("/proc/cpuinfo");
            if (cpuinfo2.is_open()) {
                std::unordered_map<std::string, int> coresPerPackage;
                std::string currentPhysicalId;

                std::string line;
                while (std::getline(cpuinfo2, line)) {
                    if (line.find("physical id") != std::string::npos) {
                        if (const auto pos = line.find(':');
                            pos != std::string::npos && pos + 2 < line.size()) {
                            currentPhysicalId = line.substr(pos + 2);
                        }
                    } else if (line.find("cpu cores") != std::string::npos &&
                               !currentPhysicalId.empty()) {
                        if (const auto pos = line.find(':');
                            pos != std::string::npos && pos + 2 < line.size()) {
                            try {
                                const auto cores =
                                    std::stoi(line.substr(pos + 2));
                                coresPerPackage[currentPhysicalId] = cores;
                            } catch (const std::exception& e) {
                                spdlog::debug("Error parsing CPU cores: {}",
                                              e.what());
                            }
                        }
                    }
                }

                // Sum cores across all packages
                numberOfCores = std::accumulate(
                    coresPerPackage.begin(), coresPerPackage.end(), 0,
                    [](int sum, const auto& package) {
                        return sum + package.second;
                    });
            }
        }

        // Last resort: count CPU directories and estimate
        if (numberOfCores <= 0) [[unlikely]] {
            spdlog::debug("Using directory counting approach as last resort");

            if (const auto dir = opendir("/sys/devices/system/cpu/");
                dir != nullptr) {
                struct dirent* entry;
                const std::regex cpuRegex("cpu[0-9]+");

                while ((entry = readdir(dir)) != nullptr) {
                    const std::string name = entry->d_name;
                    if (std::regex_match(name, cpuRegex)) {
                        ++numberOfCores;
                    }
                }
                closedir(dir);

                // Attempt to account for hyperthreading (rough estimate)
                numberOfCores = std::max(1, numberOfCores / 2);
            } else {
                numberOfCores = getNumberOfLogicalCores();
                spdlog::warn(
                    "Could not determine physical CPU cores, using logical "
                    "count: {}",
                    numberOfCores);
            }
        }

        // Ensure at least one core
        if (numberOfCores <= 0) {
            numberOfCores = 1;
            spdlog::warn(
                "Could not determine number of physical CPU cores, assuming 1");
        }

        // Cache the result
        cached_cores.store(numberOfCores, std::memory_order_release);

    } catch (const std::exception& e) {
        spdlog::error("Exception in getNumberOfPhysicalCores: {}", e.what());
        return 1;
    }

    spdlog::info("Linux Physical CPU Cores: {}", numberOfCores);
    return numberOfCores;
}

auto getNumberOfLogicalCores() -> int {
    spdlog::debug("getNumberOfLogicalCores_Linux: Counting logical CPU cores");

    // Use static cache for logical core count (doesn't change during runtime)
    static std::atomic<int> cached_logical_cores{0};
    if (const auto cached =
            cached_logical_cores.load(std::memory_order_acquire);
        cached > 0) {
        spdlog::debug("Using cached logical core count: {}", cached);
        return cached;
    }

    auto numberOfCores = 0;

    try {
        // First try sysconf (fastest)
        numberOfCores = sysconf(_SC_NPROCESSORS_ONLN);

        if (numberOfCores > 0) {
            spdlog::debug("Got logical core count from sysconf: {}",
                          numberOfCores);
        } else {
            // Fallback: count processors in /proc/cpuinfo
            spdlog::debug("sysconf failed, trying /proc/cpuinfo");

            std::ifstream cpuinfo("/proc/cpuinfo");
            if (cpuinfo.is_open()) {
                std::string line;
                while (std::getline(cpuinfo, line)) {
                    if (line.find("processor") != std::string::npos) {
                        ++numberOfCores;
                    }
                }
                spdlog::debug("Got logical core count from /proc/cpuinfo: {}",
                              numberOfCores);
            }
        }

        // Last resort: count CPU directories
        if (numberOfCores <= 0) [[unlikely]] {
            spdlog::debug("Trying directory counting as last resort");

            if (const auto dir = opendir("/sys/devices/system/cpu/");
                dir != nullptr) {
                struct dirent* entry;
                const std::regex cpuRegex("cpu[0-9]+");

                while ((entry = readdir(dir)) != nullptr) {
                    const std::string name = entry->d_name;
                    if (std::regex_match(name, cpuRegex)) {
                        ++numberOfCores;
                    }
                }
                closedir(dir);
                spdlog::debug(
                    "Got logical core count from directory listing: {}",
                    numberOfCores);
            }
        }

        // Ensure at least one core
        if (numberOfCores <= 0) {
            numberOfCores = 1;
            spdlog::warn(
                "Could not determine number of logical CPU cores, assuming 1");
        }

        // Cache the result
        cached_logical_cores.store(numberOfCores, std::memory_order_release);

    } catch (const std::exception& e) {
        spdlog::error("Exception in getNumberOfLogicalCores: {}", e.what());
        return 1;
    }

    spdlog::info("Linux Logical CPU Cores: {}", numberOfCores);
    return numberOfCores;
}

auto getCacheSizes() -> CacheSizes {
    spdlog::debug("getCacheSizes_Linux: Reading CPU cache information");

    // Use static cache for cache sizes (hardware characteristic, doesn't
    // change)
    static std::atomic<bool> cache_info_cached{false};
    static CacheSizes cached_sizes{};
    static std::shared_mutex cache_sizes_mutex;

    // Fast path: return cached result
    {
        std::shared_lock lock(cache_sizes_mutex);
        if (cache_info_cached.load(std::memory_order_acquire)) {
            spdlog::debug("Using cached cache sizes");
            return cached_sizes;
        }
    }

    // Initialize cache sizes structure
    CacheSizes cacheSizes{};
    cacheSizes.l1d = cacheSizes.l1i = cacheSizes.l2 = cacheSizes.l3 = 0;
    cacheSizes.l1d_line_size = cacheSizes.l1i_line_size = cacheSizes.l2_line_size = cacheSizes.l3_line_size = 0;
    cacheSizes.l1d_associativity = cacheSizes.l1i_associativity = cacheSizes.l2_associativity = cacheSizes.l3_associativity = 0;

    try {
        // Modern approach: use lambda for cache info reading
        const auto readCacheInfo = [](const std::string& path,
                                      const std::string& file) -> size_t {
            std::ifstream cacheFile(path + file);
            if (!cacheFile.is_open())
                return 0;

            std::string line;
            if (!std::getline(cacheFile, line))
                return 0;

            try {
                return static_cast<size_t>(std::stoull(line));
            } catch (const std::exception& e) {
                spdlog::debug("Error parsing cache size from {}: {}",
                              path + file, e.what());
                return 0;
            }
        };

        // Check /sys/devices/system/cpu/cpu0/cache/
        constexpr std::string_view cache_base_path =
            "/sys/devices/system/cpu/cpu0/cache/"sv;

        if (const auto dir = opendir(cache_base_path.data()); dir != nullptr) {
            struct dirent* entry;

            while ((entry = readdir(dir)) != nullptr) {
                const std::string name = entry->d_name;

                // Skip . and .. entries, only process indexN directories
                if (name == "." || name == ".." || !name.starts_with("index"))
                    continue;

                const auto index_path =
                    std::string{cache_base_path} + name + "/";

                // Read cache level and type
                std::ifstream levelFile(index_path + "level");
                std::ifstream typeFile(index_path + "type");

                if (!levelFile.is_open() || !typeFile.is_open())
                    continue;

                std::string levelStr, typeStr;
                if (!std::getline(levelFile, levelStr) ||
                    !std::getline(typeFile, typeStr))
                    continue;

                try {
                    const auto level = std::stoi(levelStr);

                    // Read cache metrics
                    auto size = readCacheInfo(index_path, "size");
                    const auto lineSize =
                        readCacheInfo(index_path, "coherency_line_size");
                    const auto ways =
                        readCacheInfo(index_path, "ways_of_associativity");

                    // If size is returned in a format like "32K", convert to
                    // bytes
                    if (size <= 0) {
                        std::ifstream sizeFile(index_path + "size");
                        if (sizeFile.is_open()) {
                            std::string sizeStr;
                            if (std::getline(sizeFile, sizeStr)) {
                                size = stringToBytes(sizeStr);
                            }
                        }
                    }

                    spdlog::debug("Found cache: Level={}, Type={}, Size={}B",
                                  level, typeStr, size);

                    // Assign to appropriate cache field based on level and type
                    switch (level) {
                        case 1:
                            if (typeStr == "Data") {
                                cacheSizes.l1d = size;
                                cacheSizes.l1d_line_size = lineSize;
                                cacheSizes.l1d_associativity = ways;
                            } else if (typeStr == "Instruction") {
                                cacheSizes.l1i = size;
                                cacheSizes.l1i_line_size = lineSize;
                                cacheSizes.l1i_associativity = ways;
                            }
                            break;
                        case 2:
                            cacheSizes.l2 = size;
                            cacheSizes.l2_line_size = lineSize;
                            cacheSizes.l2_associativity = ways;
                            break;
                        case 3:
                            cacheSizes.l3 = size;
                            cacheSizes.l3_line_size = lineSize;
                            cacheSizes.l3_associativity = ways;
                            break;
                        default:
                            spdlog::debug("Unknown cache level: {}", level);
                            break;
                    }

                } catch (const std::exception& e) {
                    spdlog::debug("Error processing cache info for {}: {}",
                                  name, e.what());
                }
            }

            closedir(dir);
        } else {
            // Fallback to /proc/cpuinfo if sysfs entries not available
            spdlog::debug(
                "Could not open cache sysfs directory, falling back to "
                "/proc/cpuinfo");

            std::ifstream cpuinfo("/proc/cpuinfo");
            if (cpuinfo.is_open()) {
                std::string line;
                while (std::getline(cpuinfo, line)) {
                    if (line.find("cache size") != std::string::npos) {
                        if (const auto pos = line.find(':');
                            pos != std::string::npos && pos + 2 < line.size()) {
                            const auto sizeStr = line.substr(pos + 2);
                            const auto size = stringToBytes(sizeStr);

                            // Assume this is the largest cache (L3 or L2)
                            if (size > 0) {
                                if (size >
                                    1024 *
                                        1024) {  // Larger than 1MB is likely L3
                                    cacheSizes.l3 = size;
                                } else {  // Smaller caches are likely L2
                                    cacheSizes.l2 = size;
                                }
                            }
                        }
                    }
                }
            }
        }

        // Cache the result
        {
            std::unique_lock lock(cache_sizes_mutex);
            cached_sizes = cacheSizes;
            cache_info_cached.store(true, std::memory_order_release);
        }

    } catch (const std::exception& e) {
        spdlog::error("Exception in getCacheSizes: {}", e.what());
        return CacheSizes{};
    }

    spdlog::info("Linux Cache Sizes: L1d={}KB, L1i={}KB, L2={}KB, L3={}KB",
                 cacheSizes.l1d / 1024, cacheSizes.l1i / 1024,
                 cacheSizes.l2 / 1024, cacheSizes.l3 / 1024);

    return cacheSizes;
}

auto getCpuLoadAverage() -> LoadAverage {
    spdlog::info("Starting getCpuLoadAverage function on Linux");

    LoadAverage loadAvg{};
    loadAvg.oneMinute = loadAvg.fiveMinutes = loadAvg.fifteenMinutes = 0.0;
    loadAvg.running_processes = loadAvg.total_processes = 0;

    double avg[3];
    if (getloadavg(avg, 3) == 3) {
        loadAvg.oneMinute = avg[0];
        loadAvg.fiveMinutes = avg[1];
        loadAvg.fifteenMinutes = avg[2];
    }

    // Alternative approach if getloadavg fails
    if (loadAvg.oneMinute <= 0.0 && loadAvg.fiveMinutes <= 0.0 &&
        loadAvg.fifteenMinutes <= 0.0) {
        std::ifstream loadFile("/proc/loadavg");
        if (loadFile.is_open()) {
            loadFile >> loadAvg.oneMinute >> loadAvg.fiveMinutes >>
                loadAvg.fifteenMinutes;
        }
    }

    spdlog::info("Linux Load Average: {}, {}, {}", loadAvg.oneMinute,
                 loadAvg.fiveMinutes, loadAvg.fifteenMinutes);

    return loadAvg;
}

auto getCpuPowerInfo() -> CpuPowerInfo {
    spdlog::info("Starting getCpuPowerInfo function on Linux");

    CpuPowerInfo powerInfo{};
    powerInfo.currentWatts = powerInfo.maxTDP = powerInfo.energyImpact = 0.0;
    powerInfo.baseWatts = powerInfo.peakWatts = powerInfo.averageWatts = 0.0;
    powerInfo.voltage = powerInfo.current = powerInfo.power_efficiency = 0.0;
    powerInfo.energy_consumed = 0;
    powerInfo.power_limit_throttling = false;

    // Try to read from RAPL interface if available
    // Energy usage in microjoules
    std::ifstream energyFile(
        "/sys/class/powercap/intel-rapl/intel-rapl:0/energy_uj");
    if (energyFile.is_open()) {
        static unsigned long long lastEnergy = 0;
        static std::chrono::steady_clock::time_point lastTime =
            std::chrono::steady_clock::now();

        unsigned long long energy;
        energyFile >> energy;

        auto now = std::chrono::steady_clock::now();
        auto elapsedMicroseconds =
            std::chrono::duration_cast<std::chrono::microseconds>(now -
                                                                  lastTime)
                .count();

        if (lastEnergy > 0 && elapsedMicroseconds > 0) {
            // Calculate power in watts (energy in microjoules / time in
            // microseconds)
            unsigned long long energyDelta = energy - lastEnergy;
            powerInfo.currentWatts =
                static_cast<double>(energyDelta) / elapsedMicroseconds;
        }

        lastEnergy = energy;
        lastTime = now;
    }

    // Try to read TDP from various possible locations
    std::ifstream tdpFile(
        "/sys/class/powercap/intel-rapl/intel-rapl:0/"
        "constraint_0_power_limit_uw");
    if (tdpFile.is_open()) {
        unsigned long long tdpUw;
        tdpFile >> tdpUw;
        powerInfo.maxTDP = static_cast<double>(tdpUw) /
                           1000000.0;  // Convert microWatts to Watts
    }

    spdlog::info(
        "Linux CPU Power Info: currentWatts={}, maxTDP={}, energyImpact={}",
        powerInfo.currentWatts, powerInfo.maxTDP, powerInfo.energyImpact);

    return powerInfo;
}

auto getCpuFeatureFlags() -> std::vector<std::string> {
    spdlog::debug("getCpuFeatureFlags_Linux: Reading CPU feature flags");

    // Use static cache for feature flags (hardware characteristic, doesn't
    // change)
    static std::atomic<bool> flags_cached{false};
    static std::vector<std::string> cached_flags;
    static std::shared_mutex flags_mutex;

    // Fast path: return cached result
    {
        std::shared_lock lock(flags_mutex);
        if (flags_cached.load(std::memory_order_acquire) &&
            !cached_flags.empty()) {
            spdlog::debug("Using cached CPU flags ({} features)",
                          cached_flags.size());
            return cached_flags;
        }
    }

    std::vector<std::string> flags;
    flags.reserve(64);  // Reserve space for typical flag count

    try {
        std::ifstream cpuinfo("/proc/cpuinfo");
        if (!cpuinfo.is_open()) [[unlikely]] {
            spdlog::error("Failed to open /proc/cpuinfo for feature flags");
            return {};
        }

        std::string line;
        while (std::getline(cpuinfo, line)) {
            // Different architectures use different field names
            const auto is_flags_line =
                line.find("flags") != std::string::npos ||
                line.find("Features") !=
                    std::string::npos;  // ARM uses "Features"

            if (is_flags_line) {
                if (const auto pos = line.find(':');
                    pos != std::string::npos && pos + 2 < line.size()) {
                    const auto flagsStr = line.substr(pos + 2);
                    std::istringstream ss(flagsStr);
                    std::string flag;

                    while (ss >> flag) {
                        flags.emplace_back(
                            std::move(flag));  // Use move semantics
                    }

                    break;  // Only need one set of flags
                }
            }
        }

        // Cache the result
        {
            std::unique_lock lock(flags_mutex);
            cached_flags = flags;
            flags_cached.store(true, std::memory_order_release);
        }

    } catch (const std::exception& e) {
        spdlog::error("Exception in getCpuFeatureFlags: {}", e.what());
        return {};
    }

    spdlog::info("Linux CPU Flags: {} features collected", flags.size());
    return flags;
}

auto getCpuArchitecture() -> CpuArchitecture {
    spdlog::debug("getCpuArchitecture_Linux: Determining CPU architecture");

    // Use static cache for architecture (never changes)
    static std::atomic<CpuArchitecture> cached_arch{CpuArchitecture::UNKNOWN};
    if (const auto cached = cached_arch.load(std::memory_order_acquire);
        cached != CpuArchitecture::UNKNOWN) {
        spdlog::debug("Using cached CPU architecture: {}",
                      cpuArchitectureToString(cached));
        return cached;
    }

    auto arch = CpuArchitecture::UNKNOWN;

    try {
        // Get architecture using uname
        struct utsname sysInfo;
        if (uname(&sysInfo) != 0) [[unlikely]] {
            spdlog::error("Failed to get system information via uname");
            return CpuArchitecture::UNKNOWN;
        }

        const std::string_view machine = sysInfo.machine;

        // Modern approach: use constexpr mapping
        constexpr struct {
            std::string_view pattern;
            CpuArchitecture arch;
        } arch_mappings[] = {
            {"x86_64"sv, CpuArchitecture::X86_64},
            {"i386"sv, CpuArchitecture::X86},
            {"i686"sv, CpuArchitecture::X86},
            {"aarch64"sv, CpuArchitecture::ARM64},
            {"arm64"sv, CpuArchitecture::ARM64},
        };

        // Check for exact matches first
        for (const auto& [pattern, target_arch] : arch_mappings) {
            if (machine == pattern) {
                arch = target_arch;
                break;
            }
        }

        // Check for partial matches if no exact match found
        if (arch == CpuArchitecture::UNKNOWN) {
            if (machine.find("arm") != std::string_view::npos) {
                arch = CpuArchitecture::ARM;
            } else if (machine.find("ppc") != std::string_view::npos ||
                       machine.find("powerpc") != std::string_view::npos) {
                arch = CpuArchitecture::POWERPC;
            } else if (machine.find("mips") != std::string_view::npos) {
                arch = CpuArchitecture::MIPS;
            } else if (machine.find("riscv") != std::string_view::npos) {
                arch = CpuArchitecture::RISC_V;
            }
        }

        // Cache the result
        cached_arch.store(arch, std::memory_order_release);

    } catch (const std::exception& e) {
        spdlog::error("Exception in getCpuArchitecture: {}", e.what());
        return CpuArchitecture::UNKNOWN;
    }

    spdlog::info("Linux CPU Architecture: {}", cpuArchitectureToString(arch));
    return arch;
}

auto getCpuVendor() -> CpuVendor {
    spdlog::debug("getCpuVendor_Linux: Determining CPU vendor");

    // Use static cache for vendor (never changes)
    static std::atomic<CpuVendor> cached_vendor{CpuVendor::UNKNOWN};
    if (const auto cached = cached_vendor.load(std::memory_order_acquire);
        cached != CpuVendor::UNKNOWN) {
        spdlog::debug("Using cached CPU vendor: {}", cpuVendorToString(cached));
        return cached;
    }

    auto vendor = CpuVendor::UNKNOWN;
    std::string vendorString;

    try {
        std::ifstream cpuinfo("/proc/cpuinfo");
        if (!cpuinfo.is_open()) [[unlikely]] {
            spdlog::error(
                "Failed to open /proc/cpuinfo for vendor information");
            return CpuVendor::UNKNOWN;
        }

        std::string line;
        while (std::getline(cpuinfo, line)) {
            // Different CPU architectures use different fields
            const auto is_vendor_line =
                line.find("vendor_id") != std::string::npos ||  // x86
                line.find("Hardware") != std::string::npos ||   // ARM
                line.find("vendor") != std::string::npos;       // Others

            if (is_vendor_line) {
                if (const auto pos = line.find(':');
                    pos != std::string::npos && pos + 2 < line.size()) {
                    vendorString = line.substr(pos + 2);

                    // Trim whitespace using modern approach
                    if (const auto start =
                            vendorString.find_first_not_of(" \t\n\r\f\v");
                        start != std::string::npos) {
                        vendorString = vendorString.substr(start);
                        if (const auto end =
                                vendorString.find_last_not_of(" \t\n\r\f\v");
                            end != std::string::npos) {
                            vendorString = vendorString.substr(0, end + 1);
                        }
                    }
                    break;
                }
            }
        }

        // If vendor string is empty, try to get it from CPU model
        if (vendorString.empty()) {
            const auto model = getCPUModel();
            if (!model.empty() && model != "Unknown") {
                vendorString = model;
            }
        }

        vendor = getVendorFromString(vendorString);

        // Cache the result
        cached_vendor.store(vendor, std::memory_order_release);

    } catch (const std::exception& e) {
        spdlog::error("Exception in getCpuVendor: {}", e.what());
        return CpuVendor::UNKNOWN;
    }

    spdlog::info("Linux CPU Vendor: {} ({})", vendorString,
                 cpuVendorToString(vendor));
    return vendor;
}

auto getCpuSocketType() -> std::string {
    spdlog::debug(
        "getCpuSocketType_Linux: Attempting to determine CPU socket type");

    // Use static cache for socket type (hardware characteristic, doesn't
    // change)
    static std::atomic<bool> socket_cached{false};
    static std::string cached_socket;
    static std::shared_mutex socket_mutex;

    // Fast path: return cached result
    {
        std::shared_lock lock(socket_mutex);
        if (socket_cached.load(std::memory_order_acquire) &&
            !cached_socket.empty()) {
            spdlog::debug("Using cached CPU socket type: {}", cached_socket);
            return cached_socket;
        }
    }

    // Linux doesn't provide socket type directly without root access
    // This would require dmidecode or similar tools with elevated privileges
    auto socketType = std::string{"Unknown"};

    try {
        // Attempt to read from DMI if available (requires root usually)
        std::ifstream dmiFile("/sys/class/dmi/id/processor_version");
        if (dmiFile.is_open()) {
            std::string line;
            if (std::getline(dmiFile, line) && !line.empty()) {
                socketType = "DMI: " + line;
                spdlog::debug("Found socket info from DMI: {}", socketType);
            }
        }

        // Cache the result
        {
            std::unique_lock lock(socket_mutex);
            cached_socket = socketType;
            socket_cached.store(true, std::memory_order_release);
        }

    } catch (const std::exception& e) {
        spdlog::debug("Exception in getCpuSocketType: {}", e.what());
        socketType = "Unknown";
    }

    spdlog::info("Linux CPU Socket Type: {} (limited access)", socketType);
    return socketType;
}

auto getCpuScalingGovernor() -> std::string {
    spdlog::debug("getCpuScalingGovernor_Linux: Reading CPU scaling governor");

    try {
        // Get the scaling governor for CPU 0 (representative)
        std::ifstream govFile(
            "/sys/devices/system/cpu/cpu0/cpufreq/scaling_governor");
        if (!govFile.is_open()) [[unlikely]] {
            spdlog::debug("Failed to open scaling governor file");
            return "Unknown";
        }

        std::string governor;
        if (!std::getline(govFile, governor)) [[unlikely]] {
            spdlog::debug("Failed to read scaling governor");
            return "Unknown";
        }

        // Trim whitespace
        if (const auto start = governor.find_first_not_of(" \t\r\n");
            start != std::string::npos) {
            governor = governor.substr(start);
            if (const auto end = governor.find_last_not_of(" \t\r\n");
                end != std::string::npos) {
                governor = governor.substr(0, end + 1);
            }
        }

        spdlog::info("Linux CPU Scaling Governor: {}", governor);
        return governor;

    } catch (const std::exception& e) {
        spdlog::error("Exception in getCpuScalingGovernor: {}", e.what());
        return "Unknown";
    }
}

auto getPerCoreScalingGovernors() -> std::vector<std::string> {
    spdlog::debug(
        "getPerCoreScalingGovernors_Linux: Reading per-core scaling governors");

    const auto numCores = getNumberOfLogicalCores();
    std::vector<std::string> governors;
    governors.reserve(numCores);

    try {
        for (int i = 0; i < numCores; ++i) {
            const auto govPath = std::format(
                "/sys/devices/system/cpu/cpu{}/cpufreq/scaling_governor", i);
            std::ifstream govFile(govPath);

            std::string governor = "Unknown";
            if (govFile.is_open()) {
                if (std::getline(govFile, governor)) {
                    // Trim whitespace
                    if (const auto start =
                            governor.find_first_not_of(" \t\r\n");
                        start != std::string::npos) {
                        governor = governor.substr(start);
                        if (const auto end =
                                governor.find_last_not_of(" \t\r\n");
                            end != std::string::npos) {
                            governor = governor.substr(0, end + 1);
                        }
                    }
                }
            }

            governors.emplace_back(std::move(governor));
        }

    } catch (const std::exception& e) {
        spdlog::error("Exception in getPerCoreScalingGovernors: {}", e.what());
        return std::vector<std::string>(numCores, "Unknown");
    }

    spdlog::info("Linux Per-Core CPU Scaling Governors: {} cores configured",
                 governors.size());
    return governors;
}

// Enhanced Linux-specific CPU functions for new features

/**
 * @brief Get CPU topology information including NUMA nodes
 * @return CpuTopology structure with detailed topology information
 */
auto getCpuTopology() -> CpuTopology {
    spdlog::debug("getCpuTopology_Linux: Reading CPU topology information");

    CpuTopology topology{};

    try {
        // Get NUMA node count
        std::ifstream numa_nodes("/sys/devices/system/node/possible");
        if (numa_nodes.is_open()) {
            std::string line;
            if (std::getline(numa_nodes, line)) {
                // Parse range like "0-1" or single number "0"
                auto dash_pos = line.find('-');
                if (dash_pos != std::string::npos) {
                    topology.numa_nodes = std::stoi(line.substr(dash_pos + 1)) + 1;
                } else {
                    topology.numa_nodes = std::stoi(line) + 1;
                }
            }
        }

        // Get package count
        std::unordered_set<int> packages;
        for (int cpu = 0; cpu < getNumberOfLogicalCores(); ++cpu) {
            std::ifstream package_file(std::format("/sys/devices/system/cpu/cpu{}/topology/physical_package_id", cpu));
            if (package_file.is_open()) {
                int package_id;
                if (package_file >> package_id) {
                    packages.insert(package_id);
                }
            }
        }
        topology.packages = packages.size();

        // Calculate cores per package and threads per core
        topology.cores_per_package = getNumberOfPhysicalCores() / std::max(1, topology.packages);
        topology.threads_per_core = getNumberOfLogicalCores() / std::max(1, getNumberOfPhysicalCores());

        // Check if hyperthreading is enabled
        topology.hyperthreading_enabled = (topology.threads_per_core > 1);
        topology.numa_enabled = (topology.numa_nodes > 1);

        // Build NUMA node CPU mapping
        topology.numa_node_cpus.resize(topology.numa_nodes);
        for (int cpu = 0; cpu < getNumberOfLogicalCores(); ++cpu) {
            std::ifstream numa_file(std::format("/sys/devices/system/cpu/cpu{}/node", cpu));
            if (numa_file.is_open()) {
                int numa_node;
                if (numa_file >> numa_node && numa_node >= 0 && numa_node < topology.numa_nodes) {
                    topology.numa_node_cpus[numa_node].push_back(cpu);
                }
            }
        }

        spdlog::info("Linux CPU Topology: {} packages, {} NUMA nodes, {} cores/package, {} threads/core",
                     topology.packages, topology.numa_nodes, topology.cores_per_package, topology.threads_per_core);

    } catch (const std::exception& e) {
        spdlog::error("Exception in getCpuTopology: {}", e.what());
    }

    return topology;
}

/**
 * @brief Get CPU security and vulnerability information
 * @return CpuSecurityInfo structure with security details
 */
auto getCpuSecurityInfo() -> CpuSecurityInfo {
    spdlog::debug("getCpuSecurityInfo_Linux: Reading CPU security information");

    CpuSecurityInfo security{};

    try {
        // Read CPU vulnerabilities from /sys/devices/system/cpu/vulnerabilities/
        const std::string vuln_path = "/sys/devices/system/cpu/vulnerabilities/";

        // Common vulnerabilities to check
        const std::vector<std::string> vuln_names = {
            "spectre_v1", "spectre_v2", "meltdown", "spec_store_bypass",
            "l1tf", "mds", "tsx_async_abort", "itlb_multihit", "srbds",
            "mmio_stale_data", "retbleed", "gather_data_sampling"
        };

        for (const auto& vuln_name : vuln_names) {
            std::ifstream vuln_file(vuln_path + vuln_name);
            if (vuln_file.is_open()) {
                std::string status;
                if (std::getline(vuln_file, status)) {
                    CpuVulnerabilityInfo vuln_info;
                    vuln_info.name = vuln_name;
                    vuln_info.status = status;

                    // Parse status to determine mitigation type
                    std::string status_lower = status;
                    std::transform(status_lower.begin(), status_lower.end(),
                                 status_lower.begin(), ::tolower);

                    if (status_lower.find("not affected") != std::string::npos) {
                        vuln_info.hardware_mitigation = true;
                        vuln_info.software_mitigation = false;
                    } else if (status_lower.find("mitigation") != std::string::npos) {
                        vuln_info.hardware_mitigation = false;
                        vuln_info.software_mitigation = true;
                        vuln_info.mitigation = status;
                    } else if (status_lower.find("vulnerable") != std::string::npos) {
                        vuln_info.hardware_mitigation = false;
                        vuln_info.software_mitigation = false;
                    }

                    security.vulnerabilities.push_back(vuln_info);
                }
            }
        }

        // Get microcode information
        std::ifstream microcode_file("/proc/cpuinfo");
        if (microcode_file.is_open()) {
            std::string line;
            while (std::getline(microcode_file, line)) {
                if (line.find("microcode") != std::string::npos) {
                    auto colon_pos = line.find(':');
                    if (colon_pos != std::string::npos) {
                        security.microcode.version = line.substr(colon_pos + 1);
                        // Trim whitespace
                        security.microcode.version.erase(0, security.microcode.version.find_first_not_of(" \t"));
                        security.microcode.version.erase(security.microcode.version.find_last_not_of(" \t") + 1);
                        break;
                    }
                }
            }
        }

        spdlog::info("Linux CPU Security: Found {} vulnerabilities, microcode version: {}",
                     security.vulnerabilities.size(), security.microcode.version);

    } catch (const std::exception& e) {
        spdlog::error("Exception in getCpuSecurityInfo: {}", e.what());
    }

    return security;
}

/**
 * @brief Get CPU power states (C-states) information
 * @return Vector of available CPU power states
 */
auto getCpuPowerStates() -> std::vector<CpuPowerState> {
    spdlog::debug("getCpuPowerStates_Linux: Reading CPU C-states information");

    std::vector<CpuPowerState> states;

    try {
        // Check for C-states in /sys/devices/system/cpu/cpu0/cpuidle/
        const std::string cpuidle_path = "/sys/devices/system/cpu/cpu0/cpuidle/";

        for (int state_num = 0; state_num < 10; ++state_num) {
            std::string state_path = cpuidle_path + "state" + std::to_string(state_num) + "/name";
            std::ifstream state_file(state_path);

            if (state_file.is_open()) {
                std::string state_name;
                if (std::getline(state_file, state_name)) {
                    // Map state names to enum values
                    if (state_name.find("C0") != std::string::npos) {
                        states.push_back(CpuPowerState::C0);
                    } else if (state_name.find("C1") != std::string::npos) {
                        states.push_back(CpuPowerState::C1);
                    } else if (state_name.find("C2") != std::string::npos) {
                        states.push_back(CpuPowerState::C2);
                    } else if (state_name.find("C3") != std::string::npos) {
                        states.push_back(CpuPowerState::C3);
                    } else if (state_name.find("C6") != std::string::npos) {
                        states.push_back(CpuPowerState::C6);
                    } else if (state_name.find("C7") != std::string::npos) {
                        states.push_back(CpuPowerState::C7);
                    } else if (state_name.find("C8") != std::string::npos) {
                        states.push_back(CpuPowerState::C8);
                    } else if (state_name.find("C9") != std::string::npos) {
                        states.push_back(CpuPowerState::C9);
                    } else if (state_name.find("C10") != std::string::npos) {
                        states.push_back(CpuPowerState::C10);
                    }
                }
            } else {
                break; // No more states available
            }
        }

        spdlog::info("Linux CPU Power States: Found {} C-states", states.size());

    } catch (const std::exception& e) {
        spdlog::error("Exception in getCpuPowerStates: {}", e.what());
    }

    return states;
}

/**
 * @brief Get enhanced thermal information for the CPU
 * @return ThermalInfo structure with detailed thermal data
 */
auto getCpuThermalInfo() -> ThermalInfo {
    spdlog::debug("getCpuThermalInfo_Linux: Reading enhanced thermal information");

    ThermalInfo thermal{};
    thermal.current_temp = getCurrentCpuTemperature();
    thermal.thermal_throttling_active = false;
    thermal.throttle_state = ThermalThrottleState::NORMAL;

    try {
        // Try to read thermal zone information
        for (int zone = 0; zone < 10; ++zone) {
            std::string zone_path = "/sys/class/thermal/thermal_zone" + std::to_string(zone) + "/";

            // Check if this is a CPU thermal zone
            std::ifstream type_file(zone_path + "type");
            if (type_file.is_open()) {
                std::string zone_type;
                if (std::getline(type_file, zone_type)) {
                    if (zone_type.find("cpu") != std::string::npos ||
                        zone_type.find("x86_pkg_temp") != std::string::npos ||
                        zone_type.find("coretemp") != std::string::npos) {

                        thermal.thermal_zone = zone_type;

                        // Read critical temperature
                        std::ifstream crit_file(zone_path + "trip_point_0_temp");
                        if (crit_file.is_open()) {
                            int crit_temp_millicelsius;
                            if (crit_file >> crit_temp_millicelsius) {
                                thermal.critical_temp = crit_temp_millicelsius / 1000.0f;
                            }
                        }

                        // Read passive temperature (throttling point)
                        std::ifstream passive_file(zone_path + "trip_point_1_temp");
                        if (passive_file.is_open()) {
                            int passive_temp_millicelsius;
                            if (passive_file >> passive_temp_millicelsius) {
                                thermal.throttle_temp = passive_temp_millicelsius / 1000.0f;
                            }
                        }

                        // Calculate thermal margin
                        if (thermal.critical_temp > 0) {
                            thermal.thermal_margin = thermal.critical_temp - thermal.current_temp;
                        }

                        // Determine throttle state based on temperature
                        if (thermal.current_temp > thermal.critical_temp * 0.95f) {
                            thermal.throttle_state = ThermalThrottleState::CRITICAL_THROTTLE;
                            thermal.thermal_throttling_active = true;
                        } else if (thermal.current_temp > thermal.throttle_temp) {
                            thermal.throttle_state = ThermalThrottleState::HEAVY_THROTTLE;
                            thermal.thermal_throttling_active = true;
                        } else if (thermal.current_temp > thermal.throttle_temp * 0.9f) {
                            thermal.throttle_state = ThermalThrottleState::MODERATE_THROTTLE;
                        } else if (thermal.current_temp > thermal.throttle_temp * 0.8f) {
                            thermal.throttle_state = ThermalThrottleState::LIGHT_THROTTLE;
                        }

                        break; // Found CPU thermal zone
                    }
                }
            }
        }

        spdlog::info("Linux CPU Thermal: Current: {:.1f}°C, Critical: {:.1f}°C, Throttle: {:.1f}°C, Margin: {:.1f}°C",
                     thermal.current_temp, thermal.critical_temp, thermal.throttle_temp, thermal.thermal_margin);

    } catch (const std::exception& e) {
        spdlog::error("Exception in getCpuThermalInfo: {}", e.what());
    }

    return thermal;
}

/**
 * @brief Check if CPU is currently throttling
 * @return True if CPU is throttling due to thermal or power limits
 */
auto isCpuThrottling() -> bool {
    try {
        auto thermal = getCpuThermalInfo();
        return thermal.thermal_throttling_active;
    } catch (const std::exception& e) {
        spdlog::error("Exception in isCpuThrottling: {}", e.what());
        return false;
    }
}

}  // namespace atom::system

#endif /* __linux__ || __ANDROID__ */
