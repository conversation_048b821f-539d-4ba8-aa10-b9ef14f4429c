/*
 * disk.cpp
 *
 * Copyright (C) 2023-2024 <PERSON> <lightapt.com>
 */

/*************************************************

Date: 2024-2-21

Description: System Information Module - Disk Implementation

**************************************************/

#include "disk.hpp"

// Include all disk submodule implementations
#include "components/disk_device.hpp"
#include "components/disk_info.hpp"
#include "components/disk_monitor.hpp"
#include "components/disk_security.hpp"
#include "components/disk_analytics.hpp"
#include "components/disk_performance.hpp"
#include "common/disk_util.hpp"

namespace atom::sysinfo::disk {

// Main disk module implementation
// This file serves as the main entry point for disk functionality
// Actual implementations are in the components/ subdirectory

} // namespace atom::sysinfo::disk
