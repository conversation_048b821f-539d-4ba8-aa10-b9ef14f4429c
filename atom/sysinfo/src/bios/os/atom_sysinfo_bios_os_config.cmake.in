# atom_sysinfo_bios_os_config.cmake.in
# Configuration file for the Enhanced OS Module

@PACKAGE_INIT@

include(CMakeFindDependencyMacro)

# Find required dependencies
find_dependency(spdlog REQUIRED)
find_dependency(Threads REQUIRED)

# Platform-specific dependencies
if(WIN32)
    # Windows-specific dependencies are linked directly
elseif(UNIX AND NOT APPLE)
    # Linux-specific dependencies
    find_dependency(PkgConfig QUIET)
elseif(APPLE)
    # macOS-specific frameworks are linked directly
endif()

# Include the targets file
include("${CMAKE_CURRENT_LIST_DIR}/atom_sysinfo_bios_os_targets.cmake")

# Check that all required components are available
check_required_components(atom_sysinfo_bios_os)

# Set variables for backward compatibility
set(ATOM_SYSINFO_BIOS_OS_FOUND TRUE)
set(ATOM_SYSINFO_BIOS_OS_VERSION "@PROJECT_VERSION@")
set(ATOM_SYSINFO_BIOS_OS_LIBRARIES atom::atom_sysinfo_bios_os)

# Provide information about the package
if(NOT TARGET atom::atom_sysinfo_bios_os)
    message(FATAL_ERROR "Expected target atom::atom_sysinfo_bios_os not found!")
endif()

# Set include directories for backward compatibility
get_target_property(ATOM_SYSINFO_BIOS_OS_INCLUDE_DIRS
    atom::atom_sysinfo_bios_os INTERFACE_INCLUDE_DIRECTORIES)

message(STATUS "Found Enhanced OS Module: ${ATOM_SYSINFO_BIOS_OS_VERSION}")
message(STATUS "Enhanced OS Module libraries: ${ATOM_SYSINFO_BIOS_OS_LIBRARIES}")
