@PACKAGE_INIT@

include(CMakeFindDependencyMacro)

# Find required dependencies
find_dependency(spdlog REQUIRED)

# Platform-specific dependencies
if(WIN32)
    # Windows-specific dependencies are handled by target_link_libraries
else()
    find_dependency(Threads REQUIRED)
endif()

# Include the targets file
include("${CMAKE_CURRENT_LIST_DIR}/atom_sysinfo_sn_targets.cmake")

# Check that all required components are available
check_required_components(atom_sysinfo_sn)

# Set variables for backward compatibility
set(ATOM_SYSINFO_SN_FOUND TRUE)
set(ATOM_SYSINFO_SN_VERSION "@PROJECT_VERSION@")
set(ATOM_SYSINFO_SN_LIBRARIES atom::atom_sysinfo_sn)
set(ATOM_SYSINFO_SN_INCLUDE_DIRS "@PACKAGE_CMAKE_INSTALL_INCLUDEDIR@/atom/sysinfo/sn")

# Print information about the found package
if(NOT atom_sysinfo_sn_FIND_QUIETLY)
    message(STATUS "Found atom_sysinfo_sn: ${ATOM_SYSINFO_SN_VERSION}")
endif()
