@PACKAGE_INIT@

include(CMakeFindDependencyMacro)

# Find required dependencies based on platform
if(WIN32)
    # Windows dependencies are system libraries
elseif(APPLE)
    find_dependency(Cocoa REQUIRED)
    find_dependency(AppKit REQUIRED)
elseif(UNIX AND NOT APPLE)
    find_dependency(PkgConfig REQUIRED)
    pkg_check_modules(X11 REQUIRED x11)
endif()

# Optional dependencies
if(@CLIPBOARD_SUPPORT_OPENCV@)
    find_dependency(OpenCV REQUIRED)
endif()

if(@CLIPBOARD_SUPPORT_CIMG@)
    find_path(CIMG_INCLUDE_DIR CImg.h REQUIRED)
endif()

include("${CMAKE_CURRENT_LIST_DIR}/atom-system-clipboard-targets.cmake")

check_required_components(atom-system-clipboard)
