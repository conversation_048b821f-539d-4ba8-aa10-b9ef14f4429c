# CMakeLists.txt for Serial Module Part of the Atom Project Author: <PERSON> Qian
# License: GPL3

cmake_minimum_required(VERSION 3.21)

# Define library name
set(LIB_NAME atom-serial)

# Find all source files
file(GLOB_RECURSE SOURCES "*.cpp")
file(GLOB_RECURSE HEADERS "*.h" "*.hpp")
if(APPLE)
  file(GLOB_RECURSE MM_SOURCES "*.mm")
  list(APPEND SOURCES ${MM_SOURCES})
endif()

# Create library target
add_library(${LIB_NAME} ${SOURCES} ${HEADERS})

# Set C++ standard requirements
target_compile_features(${LIB_NAME} PUBLIC cxx_std_20)

# Add compiler-specific optimizations
if(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
    target_compile_options(${LIB_NAME} PRIVATE
        -Wall -Wextra -Wpedantic
        -O3 -march=native
        -ffast-math
    )
elseif(CMAKE_CXX_COMPILER_ID MATCHES "MSVC")
    target_compile_options(${LIB_NAME} PRIVATE
        /W4 /O2 /arch:AVX2
        /fp:fast
    )
endif()

# Setup include directories
target_include_directories(
  ${LIB_NAME} PUBLIC $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
                     $<INSTALL_INTERFACE:include/atom/serial>)

# Set platform-specific dependencies
if(WIN32)
  target_link_libraries(${LIB_NAME} PUBLIC atom-error SetupAPI
                                           Cfgmgr32)
elseif(APPLE)
  find_library(IOKIT_FRAMEWORK IOKit REQUIRED)
  find_library(FOUNDATION_FRAMEWORK Foundation REQUIRED)
  target_link_libraries(
    ${LIB_NAME} PUBLIC atom-error ${IOKIT_FRAMEWORK}
                       ${FOUNDATION_FRAMEWORK})
else() # Linux/Unix
  find_package(PkgConfig REQUIRED)
  pkg_check_modules(UDEV REQUIRED libudev)
  pkg_check_modules(LIBUSB REQUIRED libusb-1.0)

  target_include_directories(${LIB_NAME} PUBLIC ${UDEV_INCLUDE_DIRS}
                                                ${LIBUSB_INCLUDE_DIRS})

  target_link_libraries(
    ${LIB_NAME} PUBLIC atom-error ${UDEV_LIBRARIES}
                       ${LIBUSB_LIBRARIES})
endif()

# Add Bluetooth support if available
if(WIN32)
  target_link_libraries(${LIB_NAME} PUBLIC BluetoothApis)
elseif(APPLE)
  # macOS Bluetooth support is already via IOKit and Foundation
elseif(UNIX)
  pkg_check_modules(BLUEZ QUIET bluez)
  if(BLUEZ_FOUND)
    target_include_directories(${LIB_NAME} PUBLIC ${BLUEZ_INCLUDE_DIRS})
    target_link_libraries(${LIB_NAME} PUBLIC ${BLUEZ_LIBRARIES})
    target_compile_definitions(${LIB_NAME} PUBLIC HAVE_BLUEZ)
  endif()
endif()

# Add module to global target list
get_property(ATOM_MODULE_TARGETS GLOBAL PROPERTY ATOM_MODULE_TARGETS)
list(APPEND ATOM_MODULE_TARGETS ${LIB_NAME})
set_property(GLOBAL PROPERTY ATOM_MODULE_TARGETS "${ATOM_MODULE_TARGETS}")

# Installation rules
install(
  TARGETS ${LIB_NAME}
  EXPORT ${LIB_NAME}-targets
  LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
  ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
  RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
  INCLUDES
  DESTINATION ${CMAKE_INSTALL_INCLUDEDIR})

install(FILES ${HEADERS} DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/atom/serial)

# Add test executable (optional)
option(BUILD_SERIAL_TESTS "Build serial communication tests" OFF)
if(BUILD_SERIAL_TESTS)
    add_executable(test_serial_cross_platform test_cross_platform.cpp)
    target_link_libraries(test_serial_cross_platform PRIVATE ${LIB_NAME})
    target_compile_features(test_serial_cross_platform PRIVATE cxx_std_20)

    # Add test to CTest if available
    if(CMAKE_TESTING_ENABLED)
        add_test(NAME SerialCrossPlatformTest COMMAND test_serial_cross_platform)
    endif()
endif()

message(STATUS "Serial module configured with cross-platform optimizations")
